server:
  port: "8080"
  mode: "debug" # debug, release, test

log:
  level: "debug" # debug, info, warn, error
  file: "logs/app.log"
  max_size: 10 # MB
  max_age: 7 # days
  max_backups: 3

database:
  host: "${DB_HOST:localhost}"
  port: "${DB_PORT:5432}"
  user: "${DB_USER:wx_crawler}"
  password: "${DB_PASSWORD:wx_crawler}"
  name: "${DB_NAME:wx_crawler}"
  sslmode: "${DB_SSLMODE:disable}"
  max_open_conns: 100
  max_idle_conns: 50
  conn_max_lifetime: "5m"

cors:
  allowed_origins:
    - "*"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
    - "PATCH"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
  allow_credentials: true
