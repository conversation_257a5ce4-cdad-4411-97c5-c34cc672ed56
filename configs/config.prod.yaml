server:
  port: "8080"
  mode: "release"

log:
  level: "info"
  file: "logs/app.log"
  max_size: 50
  max_age: 30
  max_backups: 10

database:
  host: "localhost"         # 可通过 DB_HOST 或 WX_CRAWLER_DATABASE_HOST 覆盖
  port: "5432"              # 可通过 DB_PORT 或 WX_CRAWLER_DATABASE_PORT 覆盖
  user: "wx_crawler"        # 可通过 DB_USER 覆盖
  password: "wx_crawler"    # 可通过 DB_PASSWORD 覆盖
  name: "wx_crawler"        # 可通过 DB_NAME 覆盖
  sslmode: "require"        # 可通过 DB_SSLMODE 覆盖
  max_open_conns: 25
  max_idle_conns: 25
  conn_max_lifetime: "5m"

cors:
  allowed_origins:
    - "${FRONTEND_URL:https://www.example.com}"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
    - "PATCH"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
  allow_credentials: true
