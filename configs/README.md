# 配置文件说明

wx-crawler 项目支持多环境配置，通过 `APP_ENV` 环境变量控制配置文件的选择。

## 📁 配置文件

- **`config.dev.yaml`** - 开发环境配置（**默认**）
- **`config.prod.yaml`** - 生产环境配置

## 🔧 环境变量控制

### APP_ENV
控制使用哪个配置文件：

| APP_ENV 值 | 配置文件 | 说明 |
|-----------|----------|------|
| `dev` 或 `development` | `config.dev.yaml` | 开发环境，详细日志 |
| `prod` 或 `production` | `config.prod.yaml` | 生产环境，性能优化 |
| **未设置或其他** | `config.dev.yaml` | **默认使用开发配置** |

## 🚀 使用示例

### 本地开发（推荐）

```bash
# GoLand 直接运行或命令行直接启动
# 自动使用开发配置，无需设置环境变量
./wx-crawler

# 热重载开发
air
```

### 显式指定环境

```bash
# 开发环境（与上面效果相同）
APP_ENV=dev ./wx-crawler

# 生产环境
APP_ENV=prod ./wx-crawler

# 使用环境变量文件
export APP_ENV=prod
./wx-crawler
```

### Docker 部署

```bash
# 开发环境
docker run wx-crawler

# 生产环境
docker run -e APP_ENV=prod wx-crawler
```

### 构建脚本

```bash
# 开发构建
./build/build.sh

# 生产构建
APP_ENV=prod ./build/build.sh
```

## 📋 配置对比

### 开发环境 (config.dev.yaml)

- ✅ **Debug 模式** - 详细的调试信息
- ✅ **详细日志** - `debug` 级别日志
- ✅ **本地数据库** - 通过环境变量配置
- ✅ **开放 CORS** - 允许所有域名 (`*`) 访问
- ✅ **小连接池** - 适合本地开发 (10个连接)
- ✅ **短日志保留** - 7天，3个备份
- ✅ **完整HTTP方法** - 支持 GET/POST/PUT/DELETE/OPTIONS/PATCH
- ✅ **环境变量支持** - 数据库配置可通过环境变量覆盖

### 生产环境 (config.prod.yaml)

- 🚀 **Release 模式** - 性能优化
- 🚀 **精简日志** - `info` 级别日志
- 🚀 **生产数据库** - 通过环境变量配置
- 🚀 **严格 CORS** - 仅允许指定域名 (通过 FRONTEND_URL 配置)
- 🚀 **大连接池** - 25个连接
- 🚀 **长日志保留** - 30天，10个备份
- 🚀 **完整HTTP方法** - 支持 GET/POST/PUT/DELETE/OPTIONS/PATCH
- 🔒 **SSL 必需** - 数据库强制 SSL
- 🔒 **环境变量** - 敏感信息通过环境变量

## 🌐 CORS 配置

- 在配置文件中设置：
  - `cors.allowed_origins`: 允许的来源（数组）。生产环境禁止包含 `*`。
  - `cors.allowed_methods`: 允许的方法（数组），如 `GET/POST/PUT/DELETE/OPTIONS/PATCH`。
  - `cors.allowed_headers`: 允许的请求头（数组），如 `Content-Type/Authorization`。
  - `cors.allow_credentials`: 是否允许携带凭证（Cookie）。
- 兼容环境变量覆盖：
  - `CORS_ALLOWED_ORIGINS`（逗号分隔），优先于配置文件。例如：
    `CORS_ALLOWED_ORIGINS=https://a.com,https://b.com ./wx-crawler`

## 🗂 日志滚动（lumberjack）

- 已内置按大小/天数/备份数量滚动的文件日志，且同时输出到控制台（JSON 格式）。
- 关键配置：
  - `log.level`: `debug/info/warn/error`
  - `log.file`: 日志文件路径，如 `logs/app.log`
  - `log.max_size`: 单个文件最大大小（MB）
  - `log.max_age`: 日志保留天数
  - `log.max_backups`: 最多保留旧文件个数
- 支持环境变量覆盖：
  - `LOG_LEVEL`、`LOG_FILE`、`LOG_MAX_SIZE`、`LOG_MAX_AGE`、`LOG_MAX_BACKUPS`


## 🔒 生产环境配置

生产环境支持通过环境变量覆盖敏感配置：

```bash
# 必需的环境变量
export APP_ENV=prod
export DB_HOST=your-production-db.com
export DB_USER=app_user
export DB_PASSWORD=your-secure-password
export DB_NAME=wx_crawler
export FRONTEND_URL=https://your-domain.com

# 启动应用
./wx-crawler
```

### 支持的环境变量

| 环境变量 | 配置项 | 示例值 |
|---------|--------|--------|
| `DB_HOST` | 数据库主机 | `prod-db.company.com` |
| `DB_PORT` | 数据库端口 | `5432` |
| `DB_USER` | 数据库用户 | `app_user` |
| `DB_PASSWORD` | 数据库密码 | `secure_password` |
| `DB_NAME` | 数据库名称 | `wx_crawler` |
| `DB_SSLMODE` | SSL 模式 | `require` |
| `DB_DSN`/`SQL_DSN` | 直接指定 DSN（优先级最高；prod 建议包含 `sslmode=require`） | `******************************/db?sslmode=require` |
| `SQL_MAX_OPEN_CONNS` | 连接池最大连接数 | `25` |
| `SQL_MAX_IDLE_CONNS` | 连接池最大空闲连接数 | `25` |
| `SQL_CONN_MAX_LIFETIME` | 连接最大生命周期（支持 5m/1h） | `5m` |
| `FRONTEND_URL` | 前端URL | `https://yourdomain.com` |

## 🌐 CORS 配置说明

### 开发环境 CORS
```yaml
cors:
  allowed_origins:
    - "*"  # 允许所有域名，开发便利
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
    - "PATCH"  # 支持部分更新
  allowed_headers:
    - "Content-Type"
    - "Authorization"
  allow_credentials: true
```

### 生产环境 CORS
```yaml
cors:
  allowed_origins:
    - "${FRONTEND_URL:https://www.example.com}"  # 限制特定域名
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
    - "PATCH"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
  allow_credentials: true
```

**重要说明**：
- **开发环境**：使用 `"*"` 允许任何域名访问，便于前端开发调试
- **生产环境**：通过 `FRONTEND_URL` 环境变量限制特定域名，确保安全性
- **PATCH方法**：支持部分资源更新，适用于用户信息修改等场景

## 📝 配置优先级

配置加载的优先级（从高到低）：

1. **环境变量** - 最高优先级
2. **配置文件** - 中等优先级
3. **代码默认值** - 最低优先级

示例：
```bash
# config.prod.yaml 中设置 port: "8080"
# 但环境变量设置了 WX_CRAWLER_SERVER_PORT=9000
# 最终使用 9000 端口
export WX_CRAWLER_SERVER_PORT=9000
APP_ENV=prod ./wx-crawler
```

## 🛠️ 开发建议

### GoLand/IDE 配置

1. **直接运行** - 不设置任何环境变量，自动使用开发配置
2. **Run Configuration** - 如需生产测试，设置 `APP_ENV=prod`

### 新环境添加

如需添加新环境（如 staging），按以下步骤：

1. 创建配置文件：`config.staging.yaml`
2. 修改 `getConfigName()` 函数添加支持
3. 使用：`APP_ENV=staging ./wx-crawler`

### 配置验证

```bash
# 验证配置文件语法
go run ./cmd --dry-run  # (如果实现了该功能)

# 查看实际使用的配置
# 应用启动时会在日志中显示配置文件路径
```

## ⚠️ 注意事项

1. **敏感信息** - 生产环境敏感信息必须通过环境变量设置
2. **配置同步** - 修改配置文件后需重启应用
3. **权限控制** - 生产环境配置文件应设置适当的文件权限
4. **备份策略** - 重要配置文件应纳入版本控制和备份

## 🔗 相关文档

- [部署文档](../docs/deployment.md) - 详细的部署指南
- [开发文档](../docs/development.md) - 开发环境搭建
- [API 文档](../docs/api.md) - API 接口说明
