#!/bin/bash

# 开发环境启动脚本

set -e

echo "🚀 启动开发环境..."

# 检查是否有 Docker 和 docker-compose
if ! command -v docker &> /dev/null; then
    echo "❌ 未找到 Docker，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ 未找到 docker-compose，请先安装 docker-compose"
    exit 1
fi

# 启动数据库
echo "🗄️ 启动数据库..."
docker-compose up -d postgres

# 等待数据库启动
echo "⏳ 等待数据库启动..."
until docker-compose exec postgres pg_isready -U postgres; do
    echo "等待 PostgreSQL 启动..."
    sleep 2
done

echo "✅ 数据库启动完成"

# 启动后端（开发模式）
echo "🔧 在开发模式下，请手动运行以下命令："
echo ""
echo "后端开发服务器:"
echo "  APP_ENV=dev go run ./cmd"
echo ""
echo "前端开发服务器:"
echo "  cd web && pnpm dev"
echo ""
echo "或者直接运行 Docker 容器:"
echo "  docker-compose up --build"
echo ""
echo "🌐 访问地址:"
echo "  前端: http://localhost:3000"
echo "  后端: http://localhost:8080"
echo "  数据库: localhost:5432"
