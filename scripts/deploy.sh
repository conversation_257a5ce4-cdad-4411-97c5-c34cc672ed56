#!/bin/bash

# Docker 部署脚本

set -e

echo "🐳 开始 Docker 部署..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请启动 Docker 后重试"
    exit 1
fi

# 停止现有容器
echo "⏹️ 停止现有容器..."
docker-compose down

# 构建镜像
echo "🔨 构建 Docker 镜像..."
docker-compose build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 显示日志
echo "📝 显示服务日志（最近 20 行）..."
docker-compose logs --tail=20

echo ""
echo "✅ 部署完成！"
echo "🌐 前端访问地址: http://localhost:3000"
echo "🔗 后端 API 地址: http://localhost:8080"
echo "📚 API 文档: http://localhost:8080/docs"
echo ""
echo "📋 常用命令:"
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"