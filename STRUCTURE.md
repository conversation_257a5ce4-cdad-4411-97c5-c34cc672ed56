# 项目目录结构 (优化后)

基于 [golang-standards/project-layout](https://github.com/golang-standards/project-layout) 标准重构的目录结构：

```
wx-crawler/
├── cmd/                   # 主应用入口
│   └── main.go           # 主程序文件
├── internal/             # 私有应用代码 (不可导入)
│   ├── app/              # 应用层 (路由设置)
│   ├── handler/          # HTTP 处理器
│   ├── service/          # 业务逻辑服务 (待添加)
│   ├── repository/       # 数据访问层 (待添加)
│   ├── middleware/       # 中间件
│   ├── config/          # 配置管理
│   ├── constants/       # 常量定义
│   ├── model/           # 数据模型
│   ├── errcode/         # 错误码定义
│   ├── response/        # 响应格式化
│   ├── database/        # 数据库连接
│   └── util/            # 内部工具函数
├── pkg/                   # 可重用的公共库
│   └── logger/           # 日志库 (真正可复用)
├── api/                   # API 规范和文档
│   ├── v1/               # API v1 版本
│   │   └── openapi.yaml  # OpenAPI 规范
│   └── README.md         # API 文档说明
├── build/                 # 构建脚本和工具
│   ├── build.sh          # 单平台构建
│   ├── build-cross.sh    # 多平台构建
│   ├── ci/               # CI/CD 配置
│   ├── docker/           # Docker 相关
│   └── README.md         # 构建说明
├── configs/               # 配置文件模板
├── docs/                  # 项目文档
├── scripts/               # 项目脚本
├── web/                   # 前端项目 (Next.js)
├── logs/                  # 日志文件
├── go.mod                 # Go 模块文件
├── go.sum                 # Go 依赖锁定
├── Dockerfile             # Docker 构建文件
├── docker-compose.yml     # Docker Compose 配置
└── README.md              # 项目说明
```

## 主要优化点

### 1. 符合 Go 标准目录布局
- `/cmd` - 应用程序入口点
- `/internal` - 私有应用代码，防止被其他项目导入
- `/pkg` - 真正可重用的公共库
- `/api` - API 规范和文档
- `/build` - 构建相关文件

### 2. 清晰的代码组织
- 按功能模块组织 internal 目录
- 将路由层重命名为 app 层，更符合架构模式
- 将业务相关的包从 pkg 移动到 internal

### 3. 改进的构建流程
- 标准化的构建脚本
- 支持多平台交叉编译
- 版本信息注入

### 4. API 文档标准化
- OpenAPI 3.0 规范
- 结构化的 API 文档

这个结构支持项目的长期维护和扩展，同时遵循了 Go 社区的最佳实践。