openapi: 3.0.3
info:
  title: Wx Crawler API
  description: 微信公众号文章爬虫 API
  version: 1.0.0
  contact:
    name: API Support
    url: https://github.com/your-org/wx-crawler
servers:
  - url: http://localhost:8080/api/v1
    description: 开发环境
paths:
  /hello:
    get:
      summary: 健康检查
      description: 检查服务器和数据库连接状态
      tags:
        - health
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '500':
          description: 服务器错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /wechat/session:
    get:
      summary: 开始微信会话
      description: 创建新的微信登录会话
      tags:
        - wechat
      responses:
        '200':
          description: 成功创建会话
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionResponse'

  /wechat/qrcode:
    get:
      summary: 获取登录二维码
      description: 获取微信登录二维码
      tags:
        - wechat
      parameters:
        - name: session_id
          in: query
          required: true
          schema:
            type: string
          description: 会话ID
      responses:
        '200':
          description: 成功获取二维码
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QRCodeResponse'

components:
  schemas:
    HealthResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: "success"
        data:
          type: object
          properties:
            status:
              type: string
              example: "healthy"
            timestamp:
              type: integer
              example: **********
            version:
              type: string
              example: "1.0.0"

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          example: 500
        message:
          type: string
          example: "Internal Server Error"
        data:
          type: object
          nullable: true

    SessionResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: "success"
        data:
          type: object
          properties:
            session_id:
              type: string
              example: "abc123"

    QRCodeResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: "success"
        data:
          type: object
          properties:
            qr_code:
              type: string
              description: "二维码图片的base64编码"