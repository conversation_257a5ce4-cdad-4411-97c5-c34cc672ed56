# API 规范和文档

这个目录包含 API 的规范和文档。

## 目录结构

- `v1/` - API v1 版本
  - `openapi.yaml` - OpenAPI 3.0 规范文件

## 使用说明

### 查看 API 文档

启动服务器后，访问以下 URL 查看 Swagger 文档：
- http://localhost:8080/docs/

### 生成代码

可以使用 OpenAPI 规范文件生成客户端代码：

```bash
# 生成 TypeScript 客户端
npx @openapitools/openapi-generator-cli generate \
  -i api/v1/openapi.yaml \
  -g typescript-fetch \
  -o web/src/api/generated

# 生成 Go 客户端
openapi-generator generate \
  -i api/v1/openapi.yaml \
  -g go \
  -o pkg/client
```

### 验证规范

```bash
# 使用 swagger-codegen 验证
swagger-codegen validate -i api/v1/openapi.yaml
```