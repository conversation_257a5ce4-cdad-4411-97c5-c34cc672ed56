# 文档目录

欢迎使用 wx-crawler 项目文档。这里包含了项目的完整文档，帮助您快速了解和使用本项目。

## 📚 文档列表

### [API 文档](./api.md)
- API 接口说明
- 请求/响应格式
- 错误码定义
- 使用示例

### [部署文档](./deployment.md)
- 环境要求
- 本地开发部署
- 生产环境部署
- Docker 部署
- Kubernetes 部署
- 监控和故障排除

### [开发文档](./development.md)
- 项目结构说明
- 开发环境搭建
- 代码规范
- 测试指南
- 贡献指南

## 🚀 快速开始

如果您是第一次使用本项目，建议按以下顺序阅读文档：

1. **了解项目**: 先阅读主 [README.md](../README.md)
2. **搭建环境**: 查看 [开发文档](./development.md) 的环境搭建部分
3. **API 使用**: 参考 [API 文档](./api.md) 了解接口使用方法
4. **部署上线**: 根据 [部署文档](./deployment.md) 进行部署

## 📋 项目概述

wx-crawler 是一个基于 Go + Gin 框架的微信公众号文章爬虫项目，主要功能包括：

- 🔐 微信登录验证
- 🔍 公众号搜索
- 📄 文章内容抓取
- 🌐 RESTful API 接口
- 🛡️ 完善的错误处理
- 📊 结构化日志记录

## 🛠️ 技术栈

**后端**:
- Go 1.21+
- Gin Web 框架
- PostgreSQL 数据库
- Viper 配置管理

**前端**:
- Next.js 14
- TypeScript
- Tailwind CSS

## 📁 项目结构

```
wx-crawler/
├── cmd/                   # 应用入口
├── internal/             # 私有代码
│   ├── app/              # 应用层
│   ├── handler/          # 处理器
│   ├── middleware/       # 中间件
│   └── ...
├── pkg/                  # 公共库
├── api/                  # API 规范
├── build/                # 构建脚本
├── configs/              # 配置文件
├── docs/                 # 项目文档
└── web/                  # 前端项目
```

## 🔗 相关链接

- **项目仓库**: [GitHub](https://github.com/your-org/wx-crawler)
- **问题反馈**: [Issues](https://github.com/your-org/wx-crawler/issues)
- **更新日志**: [CHANGELOG](../CHANGELOG.md)

## 📞 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

1. 查看相关文档
2. 搜索已有的 [Issues](https://github.com/your-org/wx-crawler/issues)
3. 提交新的 Issue
4. 参与社区讨论

## 📝 文档贡献

我们欢迎您为文档做出贡献：

1. Fork 项目仓库
2. 创建文档分支
3. 编写/修改文档
4. 提交 Pull Request

请遵循项目的 [贡献指南](./development.md#贡献指南) 进行文档贡献。