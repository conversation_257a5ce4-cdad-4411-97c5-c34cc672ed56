# API 文档

## 基本信息

- **基础 URL**: `http://localhost:8080/api/v1`
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

所有 API 接口都遵循统一的响应格式：

```json
{
  "code": 0,           // 响应码，0表示成功
  "message": "success", // 响应消息
  "data": {}           // 响应数据，成功时包含具体数据
}
```

### 错误响应格式

```json
{
  "code": 12001,       // 错误码
  "message": "系统内部错误", // 错误消息
  "detail": "具体错误信息"  // 错误详情（可选）
}
```

## 错误码说明

| 错误码范围 | 说明 |
|-----------|------|
| 10000 | 成功 |
| 12xxx | 系统级别错误 |
| 21xxx | 参数验证错误 |
| 32xxx | 数据库错误 |
| 42xxx | 微信API错误 |
| 52xxx | 第三方服务错误 |

## 接口列表

### 1. 健康检查

**接口地址**: `GET /health`

**功能说明**: 检查服务器和数据库连接状态

**请求参数**: 无

**响应示例**:
```json
{
  "code": 10000,
  "message": "成功",
  "data": {
    "status": "healthy",
    "timestamp": **********,
    "version": "1.0.0"
  }
}
```

### 2. 微信相关接口

#### 2.1 开始微信会话

**接口地址**: `GET /wechat/session`

**功能说明**: 创建新的微信登录会话

**请求参数**: 无

**响应示例**:
```json
{
  "code": 10000,
  "message": "成功",
  "data": {
    "session_id": "abc123def456"
  }
}
```

#### 2.2 获取登录二维码

**接口地址**: `GET /wechat/qrcode`

**功能说明**: 获取微信登录二维码

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|-----|------|-----|------|
| session_id | string | 是 | 会话ID |

**响应示例**:
```json
{
  "code": 10000,
  "message": "成功",
  "data": {
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  }
}
```

#### 2.3 检查扫码状态

**接口地址**: `GET /wechat/scan`

**功能说明**: 检查二维码扫描状态

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|-----|------|-----|------|
| session_id | string | 是 | 会话ID |

#### 2.4 微信登录

**接口地址**: `GET /wechat/login`

**功能说明**: 完成微信登录

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|-----|------|-----|------|
| session_id | string | 是 | 会话ID |

#### 2.5 搜索公众号

**接口地址**: `GET /wechat/search-account`

**功能说明**: 搜索微信公众号

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|-----|------|-----|------|
| keyword | string | 是 | 搜索关键词 |
| session_id | string | 是 | 会话ID |

#### 2.6 搜索文章

**接口地址**: `GET /wechat/search-article`

**功能说明**: 搜索微信公众号文章

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|-----|------|-----|------|
| keyword | string | 是 | 搜索关键词 |
| session_id | string | 是 | 会话ID |

#### 2.7 下载文章

**接口地址**: `GET /wechat/download-article`

**功能说明**: 下载微信公众号文章内容

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|-----|------|-----|------|
| url | string | 是 | 文章URL |
| session_id | string | 是 | 会话ID |

## 使用示例

### curl 示例

```bash
# 健康检查
curl -X GET "http://localhost:8080/api/v1/health"

# 开始会话
curl -X GET "http://localhost:8080/api/v1/wechat/session"

# 获取二维码
curl -X GET "http://localhost:8080/api/v1/wechat/qrcode?session_id=abc123"

# 搜索公众号
curl -X GET "http://localhost:8080/api/v1/wechat/search-account?keyword=技术博客&session_id=abc123"
```

### JavaScript 示例

```javascript
// 健康检查
fetch('http://localhost:8080/api/v1/health')
  .then(response => response.json())
  .then(data => console.log(data));

// 开始会话
fetch('http://localhost:8080/api/v1/wechat/session')
  .then(response => response.json())
  .then(data => {
    const sessionId = data.data.session_id;
    // 使用会话ID进行后续操作
  });
```

## 注意事项

1. **会话管理**: 微信相关操作需要先创建会话，获取 `session_id`
2. **请求频率**: 建议控制请求频率，避免被微信限制
3. **错误处理**: 请根据错误码进行相应的错误处理
4. **CORS**: 开发环境已配置CORS，支持跨域请求