# 部署文档

## 环境要求

- **Go**: 1.21+
- **PostgreSQL**: 12+
- **Node.js**: 18+ (用于前端项目)
- **操作系统**: Linux/macOS/Windows

## 部署方式

### 1. 本地开发部署（dev）

#### 1.1 克隆项目

```bash
git clone <项目地址>
cd wx-crawler
```

#### 1.2 安装依赖

```bash
go mod download
```

#### 1.3 配置环境

```bash
# 复制配置文件
cp configs/config.dev.yaml configs/config.yaml

# 设置环境变量
export APP_ENV=dev
```

#### 1.4 启动数据库 (可选)

```bash
# 使用 Docker 启动 PostgreSQL
docker run -d \
  --name wx-crawler-db \
  -e POSTGRES_DB=wx_crawler_dev \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 \
  postgres:15
```

#### 1.5 运行应用

```bash
# 开发模式
APP_ENV=dev go run ./cmd

# 或者编译后运行
go build -o wx-crawler ./cmd
APP_ENV=dev ./wx-crawler
```

### 2. 生产环境部署（prod）

#### 2.1 使用构建脚本

```bash
# 单平台构建
./build/build.sh

# 多平台构建
VERSION=1.0.0 ./build/build-cross.sh
```

#### 2.2 配置生产环境

```bash
# 设置环境变量
export APP_ENV=prod
export DB_HOST=your-db-host
export DB_USER=your-db-user
export DB_PASSWORD=your-secure-password
export DB_NAME=wx_crawler
export DB_SSLMODE=require
export FRONTEND_URL=https://your-domain.com
```

#### 2.3 启动服务

```bash
# 直接运行
APP_ENV=prod ./bin/wx-crawler

# 或使用 systemd (推荐)
sudo cp wx-crawler /usr/local/bin/
sudo systemctl start wx-crawler
sudo systemctl enable wx-crawler
```

### 3. Docker 部署

#### 3.1 构建镜像

```bash
# 构建应用镜像
docker build -t wx-crawler:latest .
```

#### 3.2 使用 Docker Compose

```bash
# 启动完整服务栈
docker-compose up -d

# 查看日志
docker-compose logs -f
```

#### 3.3 环境变量配置

创建 `.env` 文件：

```bash
# 应用配置
APP_ENV=prod
SERVER_PORT=8080

# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your-secure-password
DB_NAME=wx_crawler
DB_SSLMODE=require

# CORS（逗号分隔，覆盖配置文件）
# CORS_ALLOWED_ORIGINS=https://your-domain.com
```

### 4. Kubernetes 部署

#### 4.1 创建 Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wx-crawler
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wx-crawler
  template:
    metadata:
      labels:
        app: wx-crawler
    spec:
      containers:
      - name: wx-crawler
        image: wx-crawler:latest
        ports:
        - containerPort: 8080
        env:
        - name: APP_ENV
          value: "prod"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: wx-crawler-secrets
              key: db-host
        # 其他环境变量...
```

#### 4.2 部署到集群

```bash
kubectl apply -f k8s/
```

## 配置说明

### 环境配置

| 环境 | 配置文件 | 说明 |
|-----|---------|------|
| 开发 | `config.dev.yaml` | 开发环境配置 |
| 生产 | `config.prod.yaml` | 生产环境配置 |

### 环境变量覆盖

| 环境变量 | 对应配置 | 示例 |
|---------|----------|------|
| `SERVER_PORT` | `server.port` | `8080` |
| `LOG_LEVEL` | `log.level` | `info` |
| `LOG_FILE` | `log.file` | `logs/app.log` |
| `LOG_MAX_SIZE` | `log.max_size` | `50` |
| `LOG_MAX_AGE` | `log.max_age` | `30` |
| `LOG_MAX_BACKUPS` | `log.max_backups` | `10` |
| `DB_HOST` | `database.host` | `prod-db.company.com` |
| `DB_PORT` | `database.port` | `5432` |
| `DB_USER` | `database.user` | `app_user` |
| `DB_PASSWORD` | `database.password` | `secure_password` |
| `DB_NAME` | `database.name` | `wx_crawler` |
| `DB_SSLMODE` | `database.sslmode` | `require` |
| `DB_DSN`/`SQL_DSN` | `database.dsn`（优先级最高；prod 建议包含 `sslmode=require`） | `******************************/db?sslmode=require` |
| `SQL_MAX_OPEN_CONNS` | `database.max_open_conns` | `25` |
| `SQL_MAX_IDLE_CONNS` | `database.max_idle_conns` | `25` |
| `SQL_CONN_MAX_LIFETIME` | `database.conn_max_lifetime` | `5m` |
| `CORS_ALLOWED_ORIGINS` | 覆盖 `cors.allowed_origins` | `https://a.com,https://b.com` |

### 重要配置项

```yaml
# 服务器配置
server:
  port: "8080"
  mode: "release"  # 生产环境使用 release

# 数据库配置
database:
  host: "${DB_HOST:localhost}"
  port: ${DB_PORT:5432}
  user: "${DB_USER:postgres}"
  password: "${DB_PASSWORD:}"
  name: "${DB_NAME:wx_crawler}"
  sslmode: "${DB_SSLMODE:require}"

# 日志配置
log:
  level: "info"
  file: "logs/prod.log"
  max_size: 50
  max_age: 30
  max_backups: 10
```

## 监控和日志

### 1. 日志管理

```bash
# 查看日志
tail -f logs/prod.log

# 日志轮转 (自动)
# 配置在 config.prod.yaml 中
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/api/v1/健康

# 预期响应
{
  "code": 10000,
  "message": "成功",
  "data": {
    "status": "healthy",
    "timestamp": **********,
    "version": "1.0.0"
  }
}
```

### 3. 性能监控

建议集成以下监控工具：

- **Prometheus**: 指标收集
- **Grafana**: 监控面板
- **Jaeger**: 链路追踪
- **ELK Stack**: 日志分析

## 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :8080
   # 杀死进程
   kill -9 <PID>
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库连接
   pg_isready -h $DB_HOST -p $DB_PORT
   # 测试连接
   psql -h $DB_HOST -U $DB_USER -d $DB_NAME
   ```

3. **配置文件问题**
   ```bash
   # 验证配置文件语法
   go run ./cmd --check-config
   ```

### 日志级别

- **debug**: 详细调试信息
- **info**: 一般信息 (生产环境推荐)
- **warn**: 警告信息
- **error**: 错误信息

## 备份和恢复

### 数据库备份

```bash
# 备份
pg_dump -h $DB_HOST -U $DB_USER $DB_NAME > backup.sql

# 恢复
psql -h $DB_HOST -U $DB_USER $DB_NAME < backup.sql
```

### 配置备份

```bash
# 备份配置
tar -czf config-backup.tar.gz configs/

# 备份日志
tar -czf logs-backup.tar.gz logs/
```

## 安全建议

1. **使用 HTTPS**: 生产环境必须使用 SSL/TLS
2. **环境变量**: 敏感信息通过环境变量传递
3. **防火墙**: 只开放必要端口
4. **更新依赖**: 定期更新 Go 模块
5. **访问控制**: 配置适当的 CORS 策略
