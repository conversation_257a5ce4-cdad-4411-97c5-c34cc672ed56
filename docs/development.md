# 开发文档

## 项目概述

wx-crawler 是一个基于 Go + Gin 框架的微信公众号文章爬虫项目，提供微信登录、公众号搜索、文章抓取等功能。

## 技术栈

### 后端
- **语言**: Go 1.21+
- **框架**: Gin
- **配置**: Viper
- **数据库**: PostgreSQL
- **日志**: slog

### 前端
- **框架**: Next.js 14
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: Zustand

## 项目结构

```
wx-crawler/
├── cmd/                   # 应用入口
│   └── main.go           # 主程序
├── internal/             # 私有代码
│   ├── app/              # 应用层 (路由)
│   ├── handler/          # HTTP 处理器
│   ├── middleware/       # 中间件
│   ├── config/          # 配置管理
│   ├── model/           # 数据模型
│   ├── constants/       # 常量
│   ├── errcode/         # 错误码
│   ├── response/        # 响应格式
│   └── database/        # 数据库连接
├── pkg/                 # 公共库
│   └── logger/          # 日志工具
├── api/                 # API 规范
├── build/               # 构建脚本
├── configs/             # 配置文件
├── docs/                # 项目文档
└── web/                 # 前端项目
```

## 开发环境搭建

### 1. 环境要求

- Go 1.21+
- Node.js 18+
- PostgreSQL 12+
- Git

### 2. 克隆项目

```bash
git clone <项目地址>
cd wx-crawler
```

### 3. 后端开发

#### 3.1 安装依赖

```bash
go mod download
```

#### 3.2 配置环境

```bash
# 复制开发配置
cp configs/config.dev.yaml configs/config.yaml

# 设置环境变量
export APP_ENV=dev
```

#### 3.3 启动开发服务

```bash
# 使用 air 进行热重载 (推荐)
go install github.com/cosmtrek/air@latest
air

# 或直接运行
go run ./cmd
```

### 4. 前端开发

```bash
cd web
npm install
npm run dev
```

## 开发指南

### 1. 代码规范

#### Go 代码规范

- 遵循 Go 官方代码风格
- 使用 `gofmt` 格式化代码
- 使用 `golint` 检查代码质量
- 函数和方法添加适当注释

```go
// Good example
func NewHealthHandler(db *sql.DB) *HealthHandler {
    return &HealthHandler{db: db}
}

// Hello 健康检查接口
// 检查服务器和数据库连接状态
func (h *HealthHandler) Hello(c *gin.Context) {
    // 实现逻辑...
}
```

#### 命名约定

- **包名**: 小写，简短有意义
- **函数**: 驼峰命名，公开函数首字母大写
- **变量**: 驼峰命名，局部变量首字母小写
- **常量**: 全大写，下划线分隔

```go
// 包名
package handler

// 常量
const MaxRetryCount = 3

// 公开函数
func NewWechatHandler() *WechatHandler {}

// 私有函数
func parseResponse(data []byte) error {}

// 变量
var sessionTimeout = 30 * time.Minute
```

### 2. 项目约定

#### 2.1 错误处理

使用统一的错误码和响应格式：

```go
// 使用预定义错误码
response.ErrorWithCode(c, errcode.DatabaseConnectFailed, err.Error())

// 或创建自定义错误
err := errcode.NewError(errcode.ParamInvalid, "缺少必要参数")
response.Error(c, err)
```

#### 2.2 日志记录

使用结构化日志：

```go
slog.Info("处理请求",
    slog.String("request_id", requestID),
    slog.String("method", c.Request.Method),
    slog.String("path", c.Request.URL.Path),
)

slog.Error("数据库连接失败",
    slog.String("error", err.Error()),
    slog.String("operation", "user_login"),
)
```

#### 2.3 配置管理

- 敏感信息通过环境变量配置
- 使用配置文件管理默认值
- 支持多环境配置

```go
// 加载配置
cfg, err := config.Load()

// 访问配置
port := cfg.Server.Port
dbHost := cfg.Database.Host
```

### 3. 数据库操作

#### 3.1 连接管理

```go
// 获取数据库连接
db, err := database.Connect(cfg.Database)
if err != nil {
    return fmt.Errorf("failed to connect database: %w", err)
}
defer db.Close()
```

#### 3.2 查询示例

```go
// 查询单条记录
var user User
err := db.QueryRow("SELECT id, name FROM users WHERE id = $1", userID).
    Scan(&user.ID, &user.Name)

// 查询多条记录
rows, err := db.Query("SELECT id, name FROM users WHERE status = $1", "active")
if err != nil {
    return err
}
defer rows.Close()

for rows.Next() {
    var user User
    err := rows.Scan(&user.ID, &user.Name)
    if err != nil {
        return err
    }
    users = append(users, user)
}
```

### 4. 中间件开发

创建自定义中间件：

```go
func CustomMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 中间件逻辑
        start := time.Now()

        // 调用下一个处理器
        c.Next()

        // 后处理逻辑
        duration := time.Since(start)
        slog.Info("请求完成", slog.Duration("duration", duration))
    }
}

// 使用中间件
r.Use(CustomMiddleware())
```

### 5. 测试

#### 5.1 单元测试

```go
func TestHealthHandler_Hello(t *testing.T) {
    // 创建测试数据库连接
    db, mock, err := sqlmock.New()
    require.NoError(t, err)
    defer db.Close()

    // 设置 mock 期望
    mock.ExpectPing()

    // 创建处理器
    handler := NewHealthHandler(db)

    // 创建测试请求
    req := httptest.NewRequest("GET", "/health", nil)
    w := httptest.NewRecorder()
    c, _ := gin.CreateTestContext(w)
    c.Request = req

    // 执行测试
    handler.Hello(c)

    // 验证结果
    assert.Equal(t, http.StatusOK, w.Code)
}
```

#### 5.2 运行测试

```bash
# 运行所有测试
go test ./...

# 运行特定包测试
go test ./internal/handler

# 运行测试并显示覆盖率
go test -cover ./...

# 生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### 6. 性能优化

#### 6.1 数据库优化

- 使用连接池
- 适当的索引
- 查询优化
- 事务管理

#### 6.2 内存优化

- 及时释放资源
- 使用对象池
- 避免内存泄漏

#### 6.3 并发处理

```go
// 使用 goroutine 处理并发任务
func processRequests(requests []Request) {
    var wg sync.WaitGroup
    semaphore := make(chan struct{}, 10) // 限制并发数

    for _, req := range requests {
        wg.Add(1)
        go func(r Request) {
            defer wg.Done()
            semaphore <- struct{}{} // 获取信号量
            defer func() { <-semaphore }() // 释放信号量

            processRequest(r)
        }(req)
    }

    wg.Wait()
}
```

## 开发工具

### 推荐 IDE

- **VS Code**: 配合 Go 插件
- **GoLand**: JetBrains 专业 IDE
- **Vim/Neovim**: 配合 vim-go

### 有用的工具

```bash
# 代码格式化
go fmt ./...

# 代码检查
golint ./...
go vet ./...

# 依赖管理
go mod tidy
go mod vendor

# 性能分析
go tool pprof

# 热重载
air

# API 测试
curl
httpie
postman
```

## 贡献指南

### 1. 分支管理

- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

### 2. 提交规范

```bash
# 提交格式
<type>(<scope>): <description>

# 示例
feat(auth): 添加微信登录功能
fix(db): 修复连接池泄漏问题
docs(api): 更新 API 文档
refactor(handler): 重构错误处理逻辑
```

### 3. Pull Request

1. 从 `develop` 创建功能分支
2. 完成开发并测试
3. 提交 PR 到 `develop`
4. 代码审查通过后合并

### 4. 发布流程

1. 从 `develop` 创建 `release` 分支
2. 测试和修复 bug
3. 合并到 `main` 并打 tag
4. 合并回 `develop`

## 常见问题

### 1. 编译问题

```bash
# 清理模块缓存
go clean -modcache

# 重新下载依赖
go mod download
```

### 2. 依赖问题

```bash
# 更新依赖
go get -u ./...

# 查看依赖
go mod graph
```

### 3. 性能问题

```bash
# 性能分析
go tool pprof -http=:8080 profile.prof

# 内存分析
go tool pprof -http=:8080 mem.prof
```

## 学习资源

- [Go 官方文档](https://golang.org/doc/)
- [Gin 框架文档](https://gin-gonic.com/docs/)
- [Go 语言圣经](https://gopl.io/)
- [Effective Go](https://golang.org/doc/effective_go.html)