# Logs
*.log
logs/

# Build outputs
dist/
build/
out/
bin/

# Dependencies
vendor/

# Go specific
# Built binaries
wx-crawler
wx-crawler-server
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Go workspace file
go.work
go.work.sum

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.idea/
*.swp
*.swo
*~

# Temporary files
cookies.txt
*.tmp
*.temp
.DS_Store
Thumbs.db

# Coverage files
coverage/
*.out

# Turbopack cache
.turbo/

# Frontend specific
web/node_modules/
web/.next/
web/.turbo/
web/dist/
web/build/
web/out/
web/tsconfig.tsbuildinfo
web/pnpm-lock.yaml
