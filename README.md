# 微信爬虫系统

一个功能完整的微信公众号爬虫系统，采用现代化的全栈架构，前后端统一部署。

## 🎯 项目概述

本系统通过微信公众号扫码登录，实现对微信数据的安全采集和管理。项目采用 Docker 容器化部署，前后端集成在同一个仓库中，简化了部署和维护流程。

### ✨ 主要特性

- 🔐 **安全登录**: 微信公众号扫码登录，安全可靠
- 🎨 **现代界面**: 基于 Next.js + shadcn/ui 的现代化前端
- ⚡ **高性能**: Go 语言后端，高并发处理能力
- 📊 **数据管理**: 完整的数据采集、存储、展示功能
- 🛡️ **安全防护**: 内置安全中间件和请求限制
- 🐳 **容器化**: Docker 统一部署，前后端一体

## 🏗️ 项目架构

```
wx-crawler/
├── web/                    # 前端项目 (Next.js)
│   ├── src/               # React 组件和页面
│   ├── public/            # 静态资源
│   ├── package.json       # 前端依赖
│   └── next.config.ts     # Next.js 配置
├── internal/              # Go 后端核心逻辑
│   ├── handler/           # HTTP 处理器
│   ├── middleware/        # 中间件
│   ├── config/           # 配置管理
│   └── util/             # 工具函数
├── cmd/                   # 应用程序入口
├── pkg/                   # 可复用包
├── docs/                  # 项目文档
├── scripts/               # 构建和部署脚本
├── Dockerfile             # Docker 多阶段构建
└── docker-compose.yml     # 容器编排配置
```

## 🚀 快速开始

### 环境要求

- Docker & Docker Compose
- 或本地开发：Go 1.24.5+ & Node.js 20+ & pnpm

### 方式一：Docker 部署（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd wx-crawler

# 一键部署
./scripts/deploy.sh
```

访问地址：
- 前端界面：http://localhost:3000
- 后端 API：http://localhost:8080
- API 文档：http://localhost:8080/docs

### 方式二：本地开发

```bash
# 启动开发环境
./scripts/dev.sh

# 然后分别启动前后端服务
# 后端
APP_ENV=dev go run ./cmd

# 前端（新终端）
cd web && pnpm dev
```

### 3. 使用系统

1. 打开浏览器访问 `http://localhost:3000`
2. 使用微信扫描页面上的二维码
3. 在手机上确认登录
4. 开始使用爬虫功能

## 🔧 配置说明

### 后端配置

环境变量（覆盖配置文件）：

- 基本
  - `APP_ENV`：环境，`dev` 或 `prod`（默认 `dev`）
  - `SERVER_PORT`：服务端口（默认 `8080`）
- 日志（已内置滚动日志）
  - `LOG_LEVEL`：`debug`/`info`/`warn`/`error`
  - `LOG_FILE`：日志文件路径，默认 `logs/app.log`
  - `LOG_MAX_SIZE`：单文件最大大小（MB），默认 `50`
  - `LOG_MAX_AGE`：保留天数，默认 `30`
  - `LOG_MAX_BACKUPS`：最多旧文件数，默认 `10`
- 数据库
  - `DB_HOST`：数据库地址（默认 `localhost`）
  - `DB_PORT`：数据库端口（默认 `5432`）
  - `DB_USER`：数据库用户名（默认 `wx_crawler`）
  - `DB_PASSWORD`：数据库密码（prod 环境必填）
  - `DB_NAME`：数据库名称（默认 `wx_crawler`）
  - `DB_SSLMODE`：`require`/`verify-ca`/`verify-full`（prod 建议 `require` 以上）
  - `DB_DSN`/`SQL_DSN`：DSN 直连（优先级最高；如 `******************************/db?sslmode=require`）
- CORS
  - `CORS_ALLOWED_ORIGINS`：逗号分隔来源列表，覆盖配置文件（如 `https://a.com,https://b.com`）

说明：同时支持带前缀的嵌套变量如 `WX_CRAWLER_DATABASE_HOST`，优先级与上述等价。

生产环境校验（APP_ENV=prod）：
- 必须提供 `DB_PASSWORD`
- `DB_SSLMODE` 不可为 `disable`
- `cors.allowed_origins` 不允许包含 `*`

### 前端配置

在 `web/next.config.ts` 中配置：

- API 代理地址
- 静态导出设置
- 图片域名白名单

## 📡 API 接口

### 核心接口

- `POST /api/v1/wechat/session` - 启动微信会话
- `GET /api/v1/wechat/qrcode` - 获取登录二维码
- `GET /api/v1/wechat/scan` - 检查扫码状态
- `POST /api/v1/wechat/login` - 完成登录流程

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 0 | 等待扫码 |
| 1 | 已扫码，等待确认 |
| 2 | 二维码过期 |
| 3 | 登录成功 |

## 🛠️ 开发指南

### 本地构建

```bash
# 构建整个项目
./scripts/build.sh

# 手动构建
cd web && pnpm build && cd ..
go build -o bin/wx-crawler ./cmd
```

### 开发工具

```bash
# 后端测试
go test ./...

# 前端测试和检查
cd web
pnpm lint
pnpm type-check
```

## 🚢 部署

### 生产部署

```bash
# 使用部署脚本
./scripts/deploy.sh

# 或手动 Docker 命令
docker-compose up -d --build
```

### 常用 Docker 命令

```bash
# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps
```

## 📝 许可证

MIT License

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改善这个项目。

## ⚠️ 免责声明

本项目仅用于学习和研究目的，请遵守相关法律法规和平台使用条款。
