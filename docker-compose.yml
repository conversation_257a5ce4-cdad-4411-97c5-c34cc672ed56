version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
      - "3000:3000"
    environment:
      - PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_NAME=wx_crawler
      - DB_SSLMODE=disable
      - LOG_LEVEL=info
      - LOG_FILE=logs/app.log
      - SERVE_FRONTEND=true
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./logs:/root/logs

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=wx_crawler
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data: