package main

import (
    "context"
    "database/sql"
    "errors"
    "fmt"
    "log"
    "log/slog"
    "net/http"
    "os"
    "os/signal"
    "strings"
    "syscall"
    "time"

    "github.com/gin-gonic/gin"
    "wx-crawler/internal/app"
    "wx-crawler/internal/config"
    "wx-crawler/internal/database"
    "wx-crawler/pkg/logger"
)

// 版本信息，通过构建时注入
var (
	version   = "dev"
	buildTime = "unknown"
	gitCommit = "unknown"
)

func main() {
    // 打印版本信息
    printVersionInfo()

    // 初始化配置
    cfg, err := config.Load()
    if err != nil {
        // 使用标准日志输出到 stderr，避免处理写入错误
        log.Printf("Failed to load config: %v", err)
        os.Exit(1)
    }

    // 标准化环境变量，后续统一使用
    env := strings.ToLower(getEnv("APP_ENV", "dev"))

    // 显示环境信息
    printEnvironmentInfo(env)

	// 初始化日志
    if err := logger.Init(cfg.Log.Level, cfg.Log.File, cfg.Log.MaxSize, cfg.Log.MaxAge, cfg.Log.MaxBackups); err != nil {
        log.Printf("Failed to initialize logger: %v", err)
        os.Exit(1)
    }

    slog.Info("Application starting",
        slog.String("version", version),
        slog.String("build_time", buildTime),
        slog.String("git_commit", gitCommit),
        slog.String("environment", env),
    )

    // 根据配置/环境设置 Gin 运行模式
    switch strings.ToLower(cfg.Server.Mode) {
    case "release":
        gin.SetMode(gin.ReleaseMode)
    default:
        // 仅支持 dev/prod：生产强制 release，其余按 debug
        if env == "prod" || env == "production" {
            gin.SetMode(gin.ReleaseMode)
        } else {
            gin.SetMode(gin.DebugMode)
        }
    }

    // 初始化数据库
    db, err := initDatabase(cfg, env)
    if err != nil {
        slog.Error("Failed to initialize database", slog.Any("err", err))
        os.Exit(1)
    }
    if db != nil {
        defer db.Close()
    }

	// 初始化并启动服务器
    server := &http.Server{
        Addr:              ":" + cfg.Server.Port,
        Handler:           app.Setup(db, cfg),
        ReadTimeout:       30 * time.Second,
        ReadHeaderTimeout: 10 * time.Second,
        WriteTimeout:      30 * time.Second,
        IdleTimeout:       120 * time.Second,
        MaxHeaderBytes:    1 << 20, // 1MB
    }

    // 启动服务器
    if err := startServer(server); err != nil {
        slog.Error("Server exited with error", slog.Any("err", err))
        return
    }
    slog.Info("Server exited gracefully")
}

// printVersionInfo 打印版本信息
func printVersionInfo() {
	fmt.Printf("wx-crawler version %s\n", version)
	fmt.Printf("Build time: %s\n", buildTime)
	fmt.Printf("Git commit: %s\n", gitCommit)
	fmt.Println()
}

// printEnvironmentInfo 打印环境信息
func printEnvironmentInfo(env string) {
    fmt.Printf("Environment: %s\n", env)
    fmt.Printf("Config file: config.%s.yaml\n", getConfigSuffix(env))
    fmt.Println()
}

// initDatabase 初始化数据库连接
func initDatabase(cfg *config.Config, env string) (*sql.DB, error) {
    db, err := database.Connect(cfg.Database)
    if err != nil {
        // 生产环境强制要求数据库连接
        if env == "prod" || env == "production" {
            return nil, fmt.Errorf("database connection required in production: %w", err)
        }

        // 开发/测试环境允许无数据库运行
        slog.Warn("Running without database connection",
            slog.Any("err", err),
            slog.String("environment", env),
        )
        return nil, nil
    }

    slog.Info("Database connected successfully")
    return db, nil
}

// startServer 启动服务器并处理优雅关闭
func startServer(server *http.Server) error {
    // 使用带上下文的信号监听，简化优雅关闭流程
    ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
    defer stop()

    errCh := make(chan error, 1)

    // 启动服务器 goroutine
    go func() {
        slog.Info("Server starting", slog.String("addr", server.Addr))
        slog.Info("Health check endpoint",
            slog.String("path", "/api/v1/health"),
            slog.String("addr", server.Addr),
        )

        if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
            errCh <- err
            return
        }
        errCh <- nil
    }()

    select {
    case <-ctx.Done():
        slog.Info("Received shutdown signal, shutting down server...")
        // 优雅关闭
        shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
        defer cancel()
        if err := server.Shutdown(shutdownCtx); err != nil {
            slog.Error("Server forced to shutdown", slog.Any("err", err))
            return err
        }
        // 确保 goroutine 退出
        <-errCh
        return nil
    case err := <-errCh:
        if err != nil {
            slog.Error("Server failed", slog.Any("err", err))
            return err
        }
        return nil
    }
}

// getEnv 获取环境变量
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getConfigSuffix 根据环境获取配置文件后缀
func getConfigSuffix(env string) string {
    switch strings.ToLower(env) {
    case "prod", "production":
        return "prod"
    default:
        return "dev"
    }
}
