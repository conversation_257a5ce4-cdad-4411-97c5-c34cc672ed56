package logger

import (
    "io"
    "log/slog"
    "os"
    "path/filepath"

    lumberjack "gopkg.in/natefinch/lumberjack.v2"
)

// Init 初始化结构化日志，支持日志文件滚动
// maxSize: 单个日志文件最大尺寸（MB）
// maxAge: 日志保留天数
// maxBackups: 保留的旧文件个数
func Init(level, logFile string, maxSize, maxAge, maxBackups int) error {
    // 确保日志目录存在
    if err := os.MkdirAll(filepath.Dir(logFile), 0755); err != nil {
        return err
    }

    // 滚动日志 writer（文件）
    rotateWriter := &lumberjack.Logger{
        Filename:   logFile,
        MaxSize:    maxSize,
        MaxAge:     maxAge,
        MaxBackups: maxBackups,
        Compress:   false,
    }

    // 同时输出到控制台和文件
    multiWriter := io.MultiWriter(os.Stdout, rotateWriter)

    // 配置结构化 JSON 日志
    handler := slog.NewJSONHandler(multiWriter, &slog.HandlerOptions{
        Level: parseLevel(level),
    })

    slog.SetDefault(slog.New(handler))
    return nil
}

func parseLevel(level string) slog.Level {
	switch level {
	case "debug":
		return slog.LevelDebug
	case "info":
		return slog.LevelInfo
	case "warn":
		return slog.LevelWarn
	case "error":
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}
