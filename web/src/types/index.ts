export interface User {
  id: string;
  fakeid: string;
  nickname: string;
  avatar: string;
  token: string;
  expire_time: number;
  login_time: number;
}

export interface BaseResp {
  err_msg: string;
  ret: number;
}

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface AudioResource {
  type: 'cover' | 'audio'
  uuid: string
  url: string
}

export interface VideoResource {
  type: 'cover' | 'video'
  uuid: string
  url: string
}

export interface VideoPageInfo {
  video_id: string

  // 是否为内嵌视频
  is_mp_video: number

  // 内嵌视频是否删除
  is_mp_video_delete: number
  cover_url: string
  cover_url_1_1: string
  cover_url_16_9: string
  mp_video_trans_info: VideoTransInfo[]
}

export interface VideoTransInfo {
  duration_ms: number
  filesize: number
  format_id: number
  url: string
  video_quality_level: number
  video_quality_wording: string
  width: number
  height: number
}
