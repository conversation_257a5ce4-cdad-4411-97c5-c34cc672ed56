import { BaseResp } from '@/types';

export interface WechatApiArticle {
  total_message_count: number;
  articles: AppMsgEx[];
}

export interface AppMsgPublishResponse {
  base_resp: BaseResp;
  is_admin: boolean;
  publish_page: PublishPage;
}

export interface PublishPage {
  total_count: number;
  publish_count: number;
  masssend_count: number;
  featured_count: number;
  publish_list: PublishItem[];
}

export interface PublishItem {
  publish_type: number;
  publish_info: PublishInfo;
}

export interface PublishInfo {
  copy_appmsg_id: number;
  copy_type: number;
  msgid: number;
  new_publish: number;
  type: number;
  sent_info: SentInfo;
  sent_result: SentResult;
  sent_status: SentStatus;
  appmsg_info: AppMsgInfo[];
  appmsgex: AppMsgEx[];
}

export interface SentInfo {
  func_flag: number;
  is_published: number;
  is_send_all: boolean;
  time: number;
}

export interface SentResult {
  msg_status: number;
  refuse_reason: string;
  update_time: number;
  reject_index_list: number[];
}

export interface SentStatus {
  fail: number;
  progress: number;
  succ: number;
  total: number;
  userprotect: number;
}

export interface AppMsgInfo {
  appmsg_like_type: number;
  appmsgid: number;
  is_from_transfer: number;
  is_pay_subscribe: number;
  item_show_type: number;
  itemidx: number;
  open_fansmsg: number;
  share_imageinfo: Record<string, unknown>[];
  share_type: number;
  smart_product: number;
  super_vote_id: number[];
  vote_id: number[];
}

export interface AppMsgEx {
  aid: string;
  album_id: string;
  appmsg_album_infos: AppMsgAlbumInfo[];
  appmsg_album_titles?: string[];
  appmsgid: number;
  author_name: string;
  ban_flag: number;
  checking: number;
  copyright_stat: number;
  copyright_type: number;
  cover: string;
  cover_img?: string;
  cover_img_theme_color?: RGB;
  digest: string;
  has_red_packet_cover: number;
  is_deleted: boolean;
  is_pay_subscribe: number;
  is_rumor_refutation?: number;
  item_show_type: number;
  itemidx: number;
  line_info?: LineInfo;
  link: string;
  media_duration: string;
  mediaapi_publish_status: number;
  mutli_picture_cover: number;
  pay_album_info?: Record<string, unknown>;
  pic_cdn_url_1_1: string;
  pic_cdn_url_3_4: string;
  pic_cdn_url_16_9: string;
  pic_cdn_url_235_1: string;
  share_imageinfo?: Record<string, unknown>[];
  tagid?: number[];
  title: string;
  content?: string;
  create_time: number;
  update_time: number;
}

export interface RGB {
  r: number;
  g: number;
  b: number;
}

export interface AppMsgAlbumInfo {
  album_id: number;
  appmsg_album_infos?: AppMsgAlbumInfo[];
  id: string;
  tagSource: number;
  title: string;
}

export interface LineInfo {
  is_appmsg_flag: number;
  is_user_flag: number;
  line_count: number;
  use_line: number;
}
