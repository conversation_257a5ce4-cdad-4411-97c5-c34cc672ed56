'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User } from '@/types';
import { AppMsgEx } from '@/types/article';
import { Account } from '@/types/account';
import {
  getUserFromStorage,
  clearAuthData,
  saveUserToStorage,
} from '@/lib/auth';
import { getSelectedAccount, setSelectedAccount } from '@/lib/accountStorage';
import { accountStorage, type StoredAccount, initDatabase } from '@/store';

interface AppState {
  user: User | null;
  selectedAccount: Account | null;
  articles: AppMsgEx[];
  accounts: Account[];
  isLoading: boolean;
  error: string | null;
  qrCodeUrl: string | null;
  scanStatus: string;
}

type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_SELECTED_ACCOUNT'; payload: Account | null }
  | { type: 'SET_ARTICLES'; payload: AppMsgEx[] }
  | { type: 'SET_ACCOUNTS'; payload: Account[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_QRCODE'; payload: string | null }
  | { type: 'SET_SCAN_STATUS'; payload: string };

const initialState: AppState = {
  user: null,
  selectedAccount: null,
  articles: [],
  accounts: [],
  isLoading: false,
  error: null,
  qrCodeUrl: null,
  scanStatus: 'waiting',
};

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'SET_SELECTED_ACCOUNT':
      return { ...state, selectedAccount: action.payload };
    case 'SET_ARTICLES':
      return { ...state, articles: action.payload };
    case 'SET_ACCOUNTS':
      return { ...state, accounts: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_QRCODE':
      return { ...state, qrCodeUrl: action.payload };
    case 'SET_SCAN_STATUS':
      return { ...state, scanStatus: action.payload };
    default:
      return state;
  }
}

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const [isInitialized, setIsInitialized] = React.useState(false);

  // 从IndexedDB加载用户信息（只在组件初始化时执行一次）
  useEffect(() => {
    const loadUserData = async () => {
      try {
        // 初始化数据库
        await initDatabase();

        // 从IndexedDB加载用户信息
        const savedUser = await getUserFromStorage();

        if (savedUser) {
          dispatch({ type: 'SET_USER', payload: savedUser });

          // 获取当前选择的公众号
          try {
            const account = await getSelectedAccount(savedUser.id);
            if (account) {
              dispatch({ type: 'SET_SELECTED_ACCOUNT', payload: account });
            }
          } catch (error) {
            console.error('Failed to load account from storage:', error);
          }

          // 清理其他相关状态
          dispatch({ type: 'SET_QRCODE', payload: null });
          dispatch({ type: 'SET_ERROR', payload: null });
          dispatch({ type: 'SET_LOADING', payload: false });
        }

        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to load user data:', error);
        setIsInitialized(true);
      }
    };

    loadUserData();
  }, []);

  // 当用户信息变化时，保存到数据库（但不在初始化时触发）
  useEffect(() => {
    if (!isInitialized) {
      return; // 初始化期间跳过保存操作
    }

    const saveUserData = async () => {
      if (state.user) {
        try {
          await saveUserToStorage(state.user);
        } catch (error) {
          console.error('将用户保存到数据库失败:', error);
        }
      } else {
        await clearAuthData();
      }
    };

    saveUserData();
  }, [state.user, isInitialized]);

  // 当当前账号变化时，保存到IndexedDB（但不在初始化时触发）
  useEffect(() => {
    if (!isInitialized) {
      return; // 初始化期间跳过保存操作
    }

    const saveAccountData = async () => {
      if (state.selectedAccount && state.user?.id) {
        try {
          const storedAccount = {
            ...state.selectedAccount,
            create_time: Date.now(),
            update_time: Date.now(),
            loaded_article_count: 0,
            total_message_count: 0,
            loaded_message_count: 0,
          };
          await accountStorage.saveSelectedAccount(
            state.user.id,
            storedAccount as StoredAccount
          );
        } catch (error) {
          console.error('Failed to save current account to IndexedDB:', error);
        }
      } else {
        if (state.user?.id) {
          try {
            await setSelectedAccount(state.user.id, null);
          } catch (error) {
            console.error(
              'Failed to clear current account from IndexedDB:',
              error
            );
          }
        }
      }
    };

    saveAccountData();
  }, [state.selectedAccount, state.user, isInitialized]);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
