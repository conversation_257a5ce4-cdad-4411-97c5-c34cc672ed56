import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 格式化时间戳为 yyyy-MM-dd HH:mm:ss 格式
 * @param timestamp 时间戳（秒）
 * @returns 格式化的时间字符串
 */
export function formatDateTime(timestamp: number): string {
  if (!timestamp || timestamp === 0) return '-';

  const date = new Date(timestamp * 1000);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 格式化媒体时长为 mm:ss 或 hh:mm:ss 格式
 * @param duration 时长（秒，可以是字符串或数字）
 * @returns 格式化的时长字符串
 */
export function formatDuration(duration: number | string): string {
  if (!duration || duration === 0 || duration === '0' || duration === '')
    return '-';

  const durationNum =
    typeof duration === 'string' ? parseInt(duration, 10) : duration;

  if (isNaN(durationNum) || durationNum <= 0) return '-';

  const hours = Math.floor(durationNum / 3600);
  const minutes = Math.floor((durationNum % 3600) / 60);
  const seconds = Math.floor(durationNum % 60);

  if (hours > 0) {
    return `${hours}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  } else {
    return `${minutes}:${String(seconds).padStart(2, '0')}`;
  }
}

/**
 * 获取微信公众号文章类型名称
 * @param type 文章类型编号
 * @returns 文章类型名称
 */
export function getArticleTypeName(type: number): string {
  switch (type) {
    case 0:
      return '普通图文';
    case 5:
      return '视频分享';
    case 6:
      return '音乐分享';
    case 7:
      return '音频分享';
    case 8:
      return '图片分享';
    case 10:
      return '文本分享';
    case 11:
      return '文章分享';
    case 17:
      return '短文';
    default:
      return `未知(${type})`;
  }
}

/**
 * 获取文章类型对应的样式类名
 * @param type 文章类型编号
 * @returns Tailwind CSS 样式类名
 */
export function getArticleTypeStyle(type: number): string {
  switch (type) {
    case 0:
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case 5:
      return 'bg-red-50 text-red-700 border-red-200';
    case 6:
      return 'bg-yellow-50 text-yellow-700 border-yellow-200';
    case 7:
      return 'bg-orange-50 text-orange-700 border-orange-200';
    case 8:
      return 'bg-green-50 text-green-700 border-green-200';
    case 10:
      return 'bg-purple-50 text-purple-700 border-purple-200';
    case 11:
      return 'bg-indigo-50 text-indigo-700 border-indigo-200';
    case 17:
      return 'bg-pink-50 text-pink-700 border-pink-200';
    default:
      return 'bg-gray-50 text-gray-600 border-gray-200';
  }
}

/**
 * 通过代理下载资源
 * @param url 资源URL
 * @param proxyUrl 代理地址
 * @param timeout 超时时间（秒），默认30秒
 * @returns Response对象
 */
export async function downloadResourceViaProxy(
  url: string, 
  proxyUrl: string,
  timeout: number = 30
): Promise<Response> {
  const proxyRequestUrl = `${proxyUrl}?url=${encodeURIComponent(url)}&preset=mp`;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout * 1000);

  try {
    const response = await fetch(proxyRequestUrl, {
      method: 'GET',
      headers: {
        'Accept': '*/*',
      },
      signal: controller.signal,
    });

    if (!response.ok) {
      throw new Error(`Failed to download resource: ${response.status} ${response.statusText}`);
    }

    return response;
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error(`Download timeout after ${timeout} seconds`);
    }
    throw error;
  } finally {
    clearTimeout(timeoutId);
  }
}

/**
 * 生成分类的唯一文件名
 * @param extension 文件扩展名
 * @param category 文件分类
 * @param index 可选的索引号
 * @returns 分类的唯一文件名
 */
export function generateCategorizedFileName(extension: string, category: 'image' | 'bg-image' | 'css' | 'audio' | 'video', index?: number): string {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const indexSuffix = index !== undefined ? `_${String(index).padStart(3, '0')}` : '';
  
  switch (category) {
    case 'image':
      return `img_${timestamp}${indexSuffix}_${randomSuffix}.${extension}`;
    case 'bg-image':
      return `bg_${timestamp}${indexSuffix}_${randomSuffix}.${extension}`;
    case 'css':
      return `style_${timestamp}${indexSuffix}_${randomSuffix}.${extension}`;
    case 'audio':
      return `audio_${timestamp}${indexSuffix}_${randomSuffix}.${extension}`;
    case 'video':
      return `video_${timestamp}${indexSuffix}_${randomSuffix}.${extension}`;
    default:
      return `file_${timestamp}${indexSuffix}_${randomSuffix}.${extension}`;
  }
}

/**
 * 格式化显示标题，超长时自动截取
 * @param title 原始标题
 * @param maxLength 最大显示长度，默认20
 * @returns 格式化后的显示标题
 */
export function formatDisplayTitle(title: string, maxLength: number = 20): string {
  return title.length > maxLength 
    ? title.slice(0, maxLength) + '...' 
    : title;
}

/**
 * 清理文件名中的非法字符，确保符合 File System API 要求
 * @param name 原始文件名
 * @returns 清理后的安全文件名
 */
function sanitizeFileName(name: string): string {
  if (!name || typeof name !== 'string') {
    return 'untitled';
  }

  let cleaned = name
    // 移除或替换 File System API 不允许的字符
    .replace(/[<>:"/\\|?*\x00-\x1f]/g, '_') // 基本非法字符
    .replace(/[\u{1F600}-\u{1F64F}]/gu, '') // 表情符号
    .replace(/[\u{1F300}-\u{1F5FF}]/gu, '') // 杂项符号
    .replace(/[\u{1F680}-\u{1F6FF}]/gu, '') // 交通符号
    .replace(/[\u{1F1E0}-\u{1F1FF}]/gu, '') // 国旗符号
    .replace(/[\u{2600}-\u{26FF}]/gu, '') // 杂项符号
    .replace(/[\u{2700}-\u{27BF}]/gu, '') // 装饰符号
    .replace(/[\u00A0-\u00BF]/g, '_') // 拉丁补充字符
    // .replace(/[【】『』「」（）〈〉《》]/g, '_') // 中文标点符号
    // .replace(/[～！？。，、；：]/g, '_') // 中文标点符号
    .replace(/[^\w\u4e00-\u9fff\-._]/g, '_') // 只保留字母、数字、中文、连字符、点和下划线
    .replace(/\s+/g, '_') // 多个空格替换为单个下划线
    .replace(/_+/g, '_') // 多个下划线替换为单个下划线
    .replace(/^[._\-]+|[._\-]+$/g, '') // 移除开头和结尾的特殊字符
    .trim();

  // File System API 特殊保留名称检查和处理
  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
  if (reservedNames.includes(cleaned.toUpperCase())) {
    cleaned = `_${cleaned}`;
  }

  // 确保名称不为空
  if (!cleaned || cleaned.length === 0) {
    cleaned = 'untitled';
  }

  // 避免以点开头（隐藏文件）
  if (cleaned.startsWith('.')) {
    cleaned = '_' + cleaned.substring(1);
  }

  // 长度限制，保留一些缓冲空间
  if (cleaned.length > 100) {
    cleaned = cleaned.substring(0, 100);
  }

  return cleaned;
}

/**
 * 生成安全的导出文件名（不包含扩展名）
 * 格式：公众号名_yyyy-MM-dd_itemidx_标题
 * @param title 文章标题
 * @param publishDate 发布时间戳（秒）
 * @param accountName 公众号名
 * @param itemIdx 文章索引
 * @returns 安全的文件名（不包含扩展名）
 */
export function generateExportFilename(
  title: string,
  publishDate?: number,
  accountName?: string,
  itemIdx?: number
): string {
  if (publishDate && accountName && itemIdx !== undefined) {
    // 格式化发布日期为 yyyy-MM-dd
    const date = new Date(publishDate * 1000);
    const dateStr = date.toISOString().split('T')[0];

    // 清理公众号名和标题中的非法字符
    const cleanAccountName = sanitizeFileName(accountName);
    const cleanTitle = sanitizeFileName(title);

    // 计算文件名总长度限制（考虑操作系统限制，留出安全边界）
    const maxFilenameLength = 180; // 总长度限制，更保守的长度
    const dateLength = 10; // 'yyyy-MM-dd'
    const itemIdxLength = itemIdx.toString().length; // itemidx长度
    const separatorLength = 3; // 三个下划线

    // 计算公众号名和标题可用的总长度
    const availableLength = maxFilenameLength - dateLength - itemIdxLength - separatorLength;

    // 为公众号名预留一定长度，剩余给标题
    const maxAccountNameLength = Math.min(cleanAccountName.length, Math.floor(availableLength * 0.3)); // 30%给公众号名
    const maxTitleLength = availableLength - maxAccountNameLength;

    // 截取公众号名和标题
    let finalAccountName = cleanAccountName.slice(0, maxAccountNameLength);
    let finalTitle = cleanTitle.slice(0, maxTitleLength);

    // 确保截取后不以特殊字符结尾
    finalAccountName = finalAccountName.replace(/[_\-\s]+$/, '');
    finalTitle = finalTitle.replace(/[_\-\s]+$/, '');

    // 如果清理后的标题为空，使用默认名称
    if (!finalTitle) {
      finalTitle = 'article';
    }

    // 如果清理后的公众号名为空，使用默认名称
    if (!finalAccountName) {
      finalAccountName = 'account';
    }

    // 按照 公众号名_yyyy-MM-dd_itemidx_标题 格式命名
    return `${finalAccountName}_${dateStr}_${itemIdx}_${finalTitle}`;
  } else {
    // 如果没有提供完整参数，则使用简化的命名方式，但也要限制长度
    const cleanTitle = sanitizeFileName(title);
    const maxLength = 180; // 限制总长度
    const result = cleanTitle.slice(0, maxLength).replace(/[_\-\s]+$/, '');
    return result || 'article';
  }
}
