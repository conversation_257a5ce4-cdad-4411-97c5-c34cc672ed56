import { User } from '@/types';
import { Account } from '@/types/account';
import { userStorage } from '../store/user';
import { accountStorage } from '../store/account';

export function isTokenValid(user: User | null): boolean {
  if (!user || !user.expire_time) return false;

  try {
    // expire_time是秒级时间戳，需要转换为毫秒进行比较
    const expireTimeMs = user.expire_time * 1000;
    return expireTimeMs > Date.now();
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return false;
  }
}

export async function saveUserToStorage(user: User): Promise<void> {
  // API已经返回了login_time，直接保存
  await userStorage.loginUser(user);
}

export async function saveAccountToStorage(
  account: Account,
  userId: string
): Promise<void> {
  await accountStorage.addAccount(account, userId);
}

export async function clearAuthData(): Promise<void> {
  // 清理数据库中的所有用户数据
  try {
    const allUsers = await userStorage.getAllUsers();
    for (const user of allUsers) {
      await userStorage.removeUser(user.id);
    }
  } catch (error) {
    console.error('Failed to clear auth data from IndexedDB:', error);
  }
}

export async function getUserFromStorage(): Promise<User | null> {
  try {
    const user = await userStorage.getCurrentUser();
    if (!user) return null;

    return isTokenValid(user) ? user : null;
  } catch (error) {
    console.error('Failed to get user from storage:', error);
    return null;
  }
}
