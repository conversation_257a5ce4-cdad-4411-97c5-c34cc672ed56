// 简化的用户存储操作，用于向后兼容
import { userStorage } from '../store/user';
import { User } from '@/types';

export async function getCurrentUser() {
  return userStorage.getCurrentUser();
}

export async function loginUser(user: User) {
  return userStorage.loginUser(user);
}

export async function getUserByToken(token: string) {
  return userStorage.getUserByToken(token);
}

export async function updateLoginTime(fakeid: string) {
  return userStorage.updateLoginTime(fakeid);
}
