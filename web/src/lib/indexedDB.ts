// 统一的IndexedDB导出
export {
  openDB,
  getDB,
  initDatabase,
  closeDatabase,
  dbManager,
} from '../store/db';
export { accountStorage } from '../store/account';
export { userStorage } from '../store/user';

// 类型导出
export type { Account } from '../types/account';
export type { User } from '../types';
export type { StoredAccount } from '../store/account';

// 向后兼容的便捷函数
export * from './userStorage';
export * from './accountStorage';
