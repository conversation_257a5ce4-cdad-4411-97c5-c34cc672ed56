import { userSettingsStorage } from '@/store/user-settings';
import PQueue from 'p-queue';
import { AudioResource, VideoResource } from "@/types";

// 代理实例结构
interface ProxyInstance {
  id: string; // 代理唯一标识
  url: string; // 代理地址
  active: boolean; // 是否激活
  busy: boolean; // 是否正在使用
  cooldown: boolean; // 是否在冷却期
  usageCount: number; // 使用次数
  successCount: number; // 总成功次数
  failureCount: number; // 总失败次数
  recentFailureCount: number; // 最近失败次数（可变动，用于判断冷却）
  traffic: number; // 下载流量（字节数）
  lastUsed?: Date; // 最后使用时间
}

// 下载资源类型
type DownloadResource = string | HTMLImageElement | HTMLLinkElement | AudioResource | VideoResource;

// 下载函数类型
type DownloadFn<T extends DownloadResource> = (resource: T, proxyUrl: string) => Promise<number>;

// 下载结果
interface DownloadResult {
  success: boolean; // 是否成功
  attempts: number; // 重试次数
  size: number; // 下载大小
  url: string; // 资源URL
  time: number; // 耗时
  error?: string; // 错误
}

/**
 * 单例代理池类
 */
class ProxyPool {
  private proxies: ProxyInstance[] = [];
  private currentUserId: string | null = null;
  private waitingQueue: Array<{
    resolve: (proxy: ProxyInstance) => void;
    reject: (error: Error) => void;
    timestamp: number;
    excludeIds?: Set<string>;
  }> = [];

  private static instance: ProxyPool;

  private constructor() {
    this.setupEventListeners();
  }

  static getInstance(): ProxyPool {
    if (!ProxyPool.instance) {
      ProxyPool.instance = new ProxyPool();
    }
    return ProxyPool.instance;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('proxyConfigUpdated', async (event: Event) => {
        const customEvent = event as CustomEvent;
        const { userId } = customEvent.detail || {};
        if (userId && this.currentUserId === userId) {
          await this.loadProxySettings(userId);
          console.log('🔄 代理池配置已自动更新');
        }
      });
    }
  }

  /**
   * 初始化代理池
   */
  async init(userId: string): Promise<void> {
    this.currentUserId = userId;
    await this.loadProxySettings(userId);
    console.log('🔧 代理池配置初始化完成');
  }

  /**
   * 清理代理池（用户登出时）
   */
  clear(): void {
    // 清理等待队列
    this.waitingQueue.forEach(waiting => {
      waiting.reject(new Error('代理池已清理'));
    });
    this.waitingQueue = [];
    
    this.proxies = [];
    this.currentUserId = null;
    console.log('🧹 代理池已清理');
  }

  /**
   * 从数据库加载代理配置并保留现有代理状态
   */
  private async loadProxySettings(userId: string): Promise<void> {
    try {
      const { proxies: proxyConfigs } = await userSettingsStorage.getProxySettings(userId);
      
      if (proxyConfigs.length === 0) {
        console.warn('⚠️ 未配置代理');
        this.proxies = [];
        return;
      }

      // 保存当前代理状态的映射 (ID -> 代理状态)
      const existingProxyStatesById = new Map<string, ProxyInstance>();
      this.proxies.forEach(proxy => {
        existingProxyStatesById.set(proxy.id, proxy);
      });

      // 创建或更新代理实例
      this.proxies = proxyConfigs.map(config => {
        const trimmedUrl = config.url.trim();
        
        // 通过ID匹配现有代理
        const existingProxy = existingProxyStatesById.get(config.id);
        
        if (existingProxy) {
          // 保留现有代理的状态，更新配置信息
          existingProxy.id = config.id;
          existingProxy.url = trimmedUrl;
          existingProxy.active = config.active;
          return existingProxy;
        } else {
          // 创建新的代理实例
          return {
            id: config.id,
            url: trimmedUrl,
            active: config.active,
            busy: false,
            cooldown: false,
            usageCount: 0,
            successCount: 0,
            failureCount: 0,
            recentFailureCount: 0,
            traffic: 0
          };
        }
      });
      
      console.log(`🌏 代理池更新完成，共 ${this.proxies.length} 个代理，可用 ${this.proxies.filter(p => p.active).length} 个代理`);
    } catch (error) {
      console.error('加载代理设置失败:', error);
      this.proxies = [];
    }
  }

  /**
   * 保存代理配置到数据库
   */
  private async saveProxySettings(): Promise<void> {
    if (!this.currentUserId) {
      console.warn('⚠️ 用户ID为空，无法保存代理配置');
      return;
    }

    try {
      const proxyConfigs = this.proxies.map(proxy => ({
        id: proxy.id,
        url: proxy.url,
        active: proxy.active
      }));

      await userSettingsStorage.setProxySettings(this.currentUserId, proxyConfigs);
      
      console.log('💾 代理配置已保存到数据库');
    } catch (error) {
      console.error('保存代理配置失败:', error);
    }
  }

  /**
   * 获取可用的代理
   * @returns {Promise<ProxyInstance>} 返回可用的代理实例
   * @throws {Error} 当60秒内无法获取到可用代理时抛出超时错误
   * 
   * 工作流程：
   * 1. 立即检查是否有可用代理（已激活 && 空闲 && 非冷却）
   * 2. 如有可用代理，选择使用次数最少的代理并标记为忙碌
   * 3. 如无可用代理，加入等待队列，等待其他代理释放后通知
   * 4. 设置60秒超时，避免无限等待
   */
  async getAvailableProxy(): Promise<ProxyInstance> {
    return this.getAvailableProxyExcluding(new Set());
  }

  /**
   * 获取可用的代理（排除指定的代理ID）
   * @param excludeIds 要排除的代理ID集合
   * @returns {Promise<ProxyInstance>} 返回可用的代理实例
   * @throws {Error} 当无法获取到可用代理时抛出错误
   */
  async getAvailableProxyExcluding(excludeIds: Set<string>): Promise<ProxyInstance> {
    // 先筛选获取可用的代理：已激活 && 非忙碌 && 非冷却 && 不在排除列表中
    const availableProxies = this.proxies.filter(proxy =>
        proxy.active && !proxy.busy && !proxy.cooldown && !excludeIds.has(proxy.id)
    );
    
    // 存在可用代理，立即返回
    if (availableProxies.length !== 0) {
      // 选择使用次数最少的代理，实现负载均衡
      const proxy = availableProxies.reduce((min, current) =>
          current.usageCount < min.usageCount ? current : min
      );
      
      // 标记代理为忙碌状态并更新使用统计
      proxy.busy = true;
      proxy.usageCount++;
      proxy.lastUsed = new Date();
      
      return proxy;
    }

    // 没有可用的代理，检查代理池状态
    if (!this.isHealthy()) {
      throw new Error('代理池不健康：所有代理都已停用');
    }

    // 检查是否所有代理都在排除列表中
    const totalActiveProxies = this.proxies.filter(proxy => proxy.active);
    const activeProxyIds = new Set(totalActiveProxies.map(proxy => proxy.id));
    const allActiveProxiesExcluded = activeProxyIds.size > 0 && Array.from(activeProxyIds).every(id => excludeIds.has(id));
    if (allActiveProxiesExcluded) {
      throw new Error('下载失败，所有代理都已尝试失败，无可用代理');
    }
    
    // 没有可用的代理，进入队列等待
    return new Promise((resolve, reject) => {
      // 设置60秒超时机制
      const timeoutId = setTimeout(() => {
        const index = this.waitingQueue.findIndex(item => item.resolve === resolve);
        if (index !== -1) {
          // 移除当前等待的请求，抛出超时异常
          this.waitingQueue.splice(index, 1);
          reject(new Error('获取代理超时：所有代理都在使用中或冷却中'));
        }
      }, 60_000);

      // 加入等待队列，等待代理释放时通知
      this.waitingQueue.push({
        resolve: (proxy) => {
          clearTimeout(timeoutId);
          resolve(proxy);
        },
        reject: (error) => {
          clearTimeout(timeoutId);
          reject(error);
        },
        timestamp: Date.now(),
        excludeIds: new Set(excludeIds) // 复制排除列表
      });
    });
  }

  /**
   * 释放代理
   * @param proxy 代理对象
   * @param success 使用当前代理的本次下载是否成功
   * @param size 本次请求传输流量大小
   */
  releaseProxy(proxy: ProxyInstance, success: boolean, size: number = 0): void {
    proxy.busy = false;
    proxy.traffic += size;
    
    if (success) {
      proxy.successCount++;
      // 成功时重置最近失败次数
      if (proxy.recentFailureCount > 0) {
        proxy.recentFailureCount = Math.max(0, proxy.recentFailureCount - 1);
      }
    } else {
      // 失败时两个计数器都增加
      proxy.failureCount++;
      proxy.recentFailureCount++;
      console.warn(`代理 [${proxy.id}]${proxy.url} 调用失败，总失败次数: ${proxy.failureCount}，最近失败次数: ${proxy.recentFailureCount}`);

      // 总失败次数大于等于 10次，并且从未成功过，标记为不可用
      if (proxy.failureCount >= 10 && proxy.successCount === 0) {
        console.warn(`代理 [${proxy.id}]${proxy.url} 连续失败过多，将被停用`)
        this.deactivateProxy(proxy.id)
      } else if (proxy.recentFailureCount >= 5) {
        // 最近连续失败 5次 及以上进入冷却期，等待 5s
        proxy.cooldown = true;

        console.warn(`代理 [${proxy.id}]${proxy.url} 进入 5s 冷却期`);
        setTimeout(() => {
          proxy.cooldown = false;
          if (proxy.active) {
            proxy.recentFailureCount = Math.max(0, proxy.recentFailureCount - 2);
            // 冷却期结束唤醒等待中的队列
            this.processWaitingQueue();
            console.warn(`代理 [${proxy.id}]${proxy.url} 冷却期结束，最近失败次数重置为: ${proxy.recentFailureCount}`);
          } else {
            proxy.recentFailureCount = 0;
          }
        }, 5_000);
      }
    }

    // 处理等待队列
    this.processWaitingQueue();
  }

  /**
   * 处理等待队列
   */
  private processWaitingQueue(): void {
    let queueIndex = 0;
    
    while (queueIndex < this.waitingQueue.length) {
      const waiting = this.waitingQueue[queueIndex];
      const excludeIds = waiting.excludeIds || new Set<string>();
      
      // 查找可用代理，排除该等待请求的黑名单
      const availableProxies = this.proxies.filter(proxy => 
        proxy.active && !proxy.busy && !proxy.cooldown && !excludeIds.has(proxy.id)
      );

      if (availableProxies.length > 0) {
        // 从队列中移除当前等待的请求
        this.waitingQueue.splice(queueIndex, 1);
        
        // 选取使用次数最少的代理
        const proxy = availableProxies.reduce((min, current) => 
          current.usageCount < min.usageCount ? current : min
        );

        proxy.busy = true;
        proxy.usageCount++;
        proxy.lastUsed = new Date();

        waiting.resolve(proxy);
        
        // 不增加queueIndex，因为当前位置的元素已被移除
      } else {
        // 检查是否所有代理都在排除列表中
        const totalActiveProxies = this.proxies.filter(proxy => proxy.active);
        const activeProxyIds = new Set(totalActiveProxies.map(p => p.id));
        const allActiveProxiesExcluded = activeProxyIds.size > 0 && Array.from(activeProxyIds).every(id => excludeIds.has(id));
        
        if (allActiveProxiesExcluded) {
          // 该等待请求无法满足，移除并拒绝
          this.waitingQueue.splice(queueIndex, 1);
          waiting.reject(new Error('所有代理都已尝试失败，无可用代理'));
          // 不增加queueIndex，因为当前位置的元素已被移除
        } else {
          // 暂时无法满足，但可能有其他代理释放，继续处理下一个请求
          queueIndex++;
        }
      }
      
      // 如果没有更多可用代理，停止处理
      if (this.proxies.filter(proxy => proxy.active && !proxy.busy && !proxy.cooldown).length === 0) {
        break;
      }
    }
  }

  /**
   * 获取代理池统计信息
   */
  getStats() {
    const total = this.proxies.length;
    const active = this.proxies.filter(p => p.active).length;
    const available = this.proxies.filter(p => p.active && !p.busy && !p.cooldown).length;
    const busy = this.proxies.filter(p => p.active && p.busy).length;
    const cooldown = this.proxies.filter(p => p.active && p.cooldown).length;
    const waiting = this.waitingQueue.length;
    const totalUsage = this.proxies.reduce((sum, p) => sum + p.usageCount, 0);
    const totalSuccess = this.proxies.reduce((sum, p) => sum + p.successCount, 0);
    const totalFailure = this.proxies.reduce((sum, p) => sum + p.failureCount, 0);
    const totalRecentFailure = this.proxies.reduce((sum, p) => sum + p.recentFailureCount, 0);
    const successRate = totalUsage > 0 ? ((totalSuccess / totalUsage) * 100).toFixed(1) : '0';
    const totalTraffic = this.proxies.reduce((sum, p) => sum + p.traffic, 0);

    return {
      total,
      active,
      available,
      busy,
      cooldown,
      waiting,
      totalUsage,
      totalSuccess,
      totalFailure,
      totalRecentFailure,
      successRate: `${successRate}%`,
      totalTraffic: this.formatTraffic(totalTraffic)
    };
  }

  /**
   * 格式化流量显示
   */
  private formatTraffic(bytes: number): string {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 ** 2) return `${(bytes / 1024).toFixed(2)} KB`;
    if (bytes < 1024 ** 3) return `${(bytes / 1024 ** 2).toFixed(2)} MB`;
    return `${(bytes / 1024 ** 3).toFixed(2)} GB`;
  }

  /**
   * 激活代理
   * @param proxyId 代理ID
   * @returns 是否成功激活
   */
  activateProxy(proxyId: string): boolean {
    const proxy = this.proxies.find(p => p.id === proxyId);
    if (proxy) {
      proxy.active = true;
      console.log(`🟢 代理 [${proxy.id}]${proxy.url} 已激活`);
      // 异步保存到数据库
      this.saveProxySettings().catch(error => {
        console.error('保存代理配置失败:', error);
      });
      return true;
    }
    return false;
  }

  /**
   * 停用代理
   * @param proxyId 代理ID  
   * @returns 是否成功停用
   */
  deactivateProxy(proxyId: string): boolean {
    const proxy = this.proxies.find(p => p.id === proxyId);
    if (proxy) {
      // 如果代理正在使用中，需要等待当前任务完成
      if (proxy.busy) {
        console.warn(`⚠️ 代理 [${proxy.id}]${proxy.url} 正在使用中，将在任务完成后停用`);
      }
      proxy.active = false;
      console.log(`🔴 代理 [${proxy.id}]${proxy.url} 已停用`);
      // 异步保存到数据库
      this.saveProxySettings().catch(error => {
        console.error('保存代理配置失败:', error);
      });
      return true;
    }
    return false;
  }

  /**
   * 根据URL激活代理（激活所有匹配的代理实例）
   * @param proxyUrl 代理URL
   * @returns 激活的代理数量
   */
  activateProxyByUrl(proxyUrl: string): number {
    const matchingProxies = this.proxies.filter(p => p.url === proxyUrl);
    let activatedCount = 0;
    
    matchingProxies.forEach(proxy => {
      if (this.activateProxy(proxy.id)) {
        activatedCount++;
      }
    });
    
    if (activatedCount > 0) {
      console.log(`🟢 已激活 ${activatedCount} 个 URL 为 ${proxyUrl} 的代理实例`);
    }
    
    return activatedCount;
  }

  /**
   * 根据URL停用代理（停用所有匹配的代理实例）
   * @param proxyUrl 代理URL
   * @returns 停用的代理数量
   */
  deactivateProxyByUrl(proxyUrl: string): number {
    const matchingProxies = this.proxies.filter(p => p.url === proxyUrl);
    let deactivatedCount = 0;
    
    matchingProxies.forEach(proxy => {
      if (this.deactivateProxy(proxy.id)) {
        deactivatedCount++;
      }
    });
    
    if (deactivatedCount > 0) {
      console.log(`🔴 已停用 ${deactivatedCount} 个 URL 为 ${proxyUrl} 的代理实例`);
    }
    
    return deactivatedCount;
  }

  /**
   * 获取所有代理列表
   */
  getAllProxies(): ProxyInstance[] {
    return [...this.proxies];
  }

  /**
   * 重置所有代理状态
   */
  resetAllProxies(): void {
    this.proxies.forEach(proxy => {
      proxy.busy = false;
      proxy.cooldown = false;
      proxy.recentFailureCount = 0; // 只重置最近失败次数，保留历史记录
    });
    console.log('🔄 所有代理状态已重置（历史失败记录保留）');
  }

  /**
   * 检查代理池是否为空
   */
  isEmpty(): boolean {
    return this.proxies.length === 0;
  }

  /**
   * 检查代理池是否健康（有可用代理或即将可用的代理）
   * @returns {boolean} 如果有可用代理或在冷却期的代理，返回true；如果所有代理都被停用，返回false
   */
  isHealthy(): boolean {
    if (this.isEmpty()) return false;
    
    // 检查是否有激活的代理（包括冷却中的代理）
    const activeProxies = this.proxies.filter(proxy => proxy.active);
    return activeProxies.length > 0;
  }

  /**
   * 检查是否有立即可用的代理
   */
  hasAvailableProxy(): boolean {
    return this.proxies.some(proxy => proxy.active && !proxy.busy && !proxy.cooldown);
  }
}

function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 使用代理 proxy 下载资源
 * @param proxy
 * @param resource
 * @param downloadFn
 */
async function downloadResource<T extends DownloadResource>(proxy: ProxyInstance, resource: T, downloadFn: DownloadFn<T>): Promise<[boolean, number]> {
  try {
    // 执行下载任务
    const size = await downloadFn(resource, proxy.url)
    return [true, size];
  } catch {
    return [false, 0];
  }
}

/**
 * 带重试机制的资源下载函数
 * @param {DownloadResource} resource - 待下载的资源
 * @param {DownloadFn} downloadFn - 下载函数，接收资源和代理URL，返回下载字节数
 * @param {boolean} useProxy - 是否使用代理，默认为true
 * @param {number} maxRetries - 最大重试次数，默认为3次
 * @returns {Promise<DownloadResult>} 下载结果，包含成功状态、重试次数、大小、URL、耗时和错误信息
 */
async function downloadWithRetry<T extends DownloadResource>(
  resource: T,
  downloadFn: DownloadFn<T>,
  useProxy: boolean = true,
  maxRetries: number = 3
): Promise<DownloadResult> {
  // 获取代理池
  const pool = ProxyPool.getInstance();

  // 获取资源地址
  let resourceUrl: string
  if (resource instanceof HTMLLinkElement) {
    resourceUrl = resource.href;
  } else if (resource instanceof HTMLImageElement) {
    resourceUrl = resource.src || resource.dataset.src || '';
  } else if (typeof resource === 'string') {
    resourceUrl = resource;
  } else if (typeof resource === 'object' && resource !== null && 'url' in resource) {
    // 处理 AudioResource 和 VideoResource 类型
    resourceUrl = (resource as AudioResource | VideoResource).url;
  } else {
    console.warn(`不支持的资源类型: ${resource}`);
    return {
      success: false,
      attempts: 0,
      size: 0,
      url: '',
      time: 0,
      error: `不支持的资源类型: ${resource}`
    };
  }
  
  // 检查资源URL是否有效
  if (!resourceUrl || resourceUrl.trim() === '') {
    console.warn('资源URL为空或无效');
    return {
      success: false,
      attempts: 0,
      size: 0,
      url: resourceUrl,
      time: 0,
      error: '资源URL为空或无效'
    };
  }
  
  // 检查代理池是否为空
  if (useProxy && pool.isEmpty()) {
    return {
      success: false,
      attempts: 0,
      size: 0,
      url: resourceUrl,
      time: 0,
      error: '未配置代理地址'
    };
  }

  // 记录本次下载尝试中失败的代理ID
  const failedProxyIds = new Set<string>();
  
  // 具体下载逻辑
  const attemptDownload = async (): Promise<{ success: boolean; size: number; error?: string; proxyId?: string }> => {
    let proxy: ProxyInstance | null = null;
    let size: number = 0;
    let success: boolean = false;
    
    try {
      if (useProxy) {
        proxy = await pool.getAvailableProxyExcluding(failedProxyIds);
        console.log(`分配代理 [${proxy.id}]${proxy.url} 下载资源 -> ${resourceUrl}`);
        [success, size] = await downloadResource(proxy, resource, downloadFn);
      } else {
        console.log(`直接下载资源 -> ${resourceUrl}`);
        [success, size] = await downloadResource<T>({} as ProxyInstance, resource, downloadFn);
      }
      return { success, size, proxyId: proxy?.id };
    } catch (error) {
      console.warn(`下载资源失败 ${resourceUrl}:`, error);
      return { 
        success: false, 
        size: 0,
        proxyId: proxy?.id,
        error: error instanceof Error ? error.message : '未知错误' 
      };
    } finally {
      // 确保代理始终被释放，只有成功获取到代理才需要释放
      if (useProxy && proxy) {
        pool.releaseProxy(proxy, success, size);
        // 如果下载失败，将代理ID加入黑名单
        if (!success) {
          failedProxyIds.add(proxy.id);
        }
      }
    }
  };

  // 开始下载资源
  const startTime = Date.now();
  let result = await attemptDownload();
  
  // 如果资源下载失败，进行重试
  let attempts = 0;
  while (!result.success && attempts < maxRetries) {
    // 在重试前检查代理池健康状态
    if (useProxy && !pool.isHealthy()) {
      console.warn(`代理池不健康，停止重试下载资源 ${resourceUrl}`);
      break;
    }
    
    if (useProxy) {
      // 检查是否所有可用代理都已失败
      const activeProxies = pool.getAllProxies().filter(p => p.active);
      const activeProxyIds = new Set(activeProxies.map(p => p.id));
      const allActiveProxiesFailed = activeProxyIds.size > 0 && Array.from(activeProxyIds).every(id => failedProxyIds.has(id));
      if (allActiveProxiesFailed) {
        console.warn(`所有可用代理都已尝试失败，停止重试下载资源 ${resourceUrl}`);
        break;
      }

      // 使用代理时，显示失败代理的URL信息
      if (failedProxyIds.size > 0) {
        const failedProxyInfo = Array.from(failedProxyIds).map(id => {
          const proxy = pool.getAllProxies().find(p => p.id === id);
          return proxy ? proxy.url : id;
        }).join(', ');
        console.log(`开始重试下载资源 ${resourceUrl} (第 ${++attempts}/${maxRetries} 次，已排除 ${failedProxyIds.size} 个失败代理: ${failedProxyInfo})`);
      } else {
        console.log(`开始重试下载资源 ${resourceUrl} (第 ${++attempts}/${maxRetries} 次)`);
      }
    } else {
      // 不使用代理或没有失败代理时的简单日志
      console.log(`开始重试下载资源 ${resourceUrl} (第 ${++attempts}/${maxRetries} 次)`);
    }
    await sleep(500);
    result = await attemptDownload();
  }

  return {
    success: result.success,
    attempts,
    size: result.size,
    url: resourceUrl,
    time: (Date.now() - startTime) / 1000,
    error: result.success ? undefined : result.error || '下载资源失败'
  };
}

// 导出的公共函数
export const proxyPool = ProxyPool.getInstance();

/**
 * 初始化用户代理池
 */
export async function initUserPool(userId: string): Promise<void> {
  await proxyPool.init(userId);
}

/**
 * 激活指定代理
 */
export function activateProxy(proxyId: string): boolean {
  return proxyPool.activateProxy(proxyId);
}

/**
 * 根据URL激活代理
 */
export function activateProxyByUrl(proxyUrl: string): number {
  return proxyPool.activateProxyByUrl(proxyUrl);
}

/**
 * 停用指定代理
 */
export function deactivateProxy(proxyId: string): boolean {
  return proxyPool.deactivateProxy(proxyId);
}

/**
 * 根据URL停用代理
 */
export function deactivateProxyByUrl(proxyUrl: string): number {
  return proxyPool.deactivateProxyByUrl(proxyUrl);
}

/**
 * 获取所有代理列表
 */
export function getAllProxies(): ProxyInstance[] {
  return proxyPool.getAllProxies();
}

/**
 * 获取代理池统计信息
 */
export function getProxyStats() {
  return proxyPool.getStats();
}

/**
 * 清理用户代理池
 */
export function clearUserPool(): void {
  proxyPool.clear();
}

/**
 * 下载单个资源
 * @param resource - 待下载的资源
 * @param downloadFn - 下载函数
 * @param useProxy - 是否使用代理，默认为true
 */
export async function download<T extends DownloadResource>(
  resource: T,
  downloadFn: DownloadFn<T>,
  useProxy: boolean = true
): Promise<DownloadResult> {
  return downloadWithRetry(resource, downloadFn, useProxy);
}

/**
 * 并发下载多个资源
 * @param resources - 待下载的资源列表
 * @param downloadFn - 下载函数
 * @param useProxy - 是否使用代理，默认为true
 * @param customConcurrency - 自定义并发数，不设置则根据代理数量自动计算
 */
export async function downloads<T extends DownloadResource>(
  resources: T[],
  downloadFn: DownloadFn<T>,
  useProxy: boolean = true,
  customConcurrency?: number
): Promise<DownloadResult[]> {
  if (resources.length === 0) {
    return [];
  }

  // 计算最优并发数
  const getConcurrency = (): number => {
    if (customConcurrency) return customConcurrency;
    
    if (!useProxy) return 3; // 不使用代理时的默认并发数
    
    const stats = proxyPool.getStats();
    const activeProxies = stats.active;
    
    if (activeProxies === 0) return 1;
    
    // 根据代理数量采用不同策略
    if (activeProxies <= 3) {
      // 少量代理：保守策略，1:1 配置
      return activeProxies;
    } else if (activeProxies <= 10) {
      // 中等代理：适度超配 20%
      return Math.ceil(activeProxies * 1.2);
    } else {
      // 大量代理：积极超配 50%
      return Math.ceil(activeProxies * 1.5);
    }
  };

  const concurrency = getConcurrency();
  const queue = new PQueue({ concurrency });

  console.log(`🚀 开始并发下载 ${resources.length} 个资源，并发数: ${concurrency}`);
  const startTime = Date.now();
  
  // 使用 PQueue 控制并发
  const tasks = resources.map(resource => 
    queue.add(() => download(resource, downloadFn, useProxy)) as Promise<DownloadResult>
  );
  
  const results = await Promise.all(tasks);

  const totalTime = (Date.now() - startTime) / 1000;
  const successCount = results.filter(r => r.success).length;
  console.log(`✅ 下载完成: ${successCount}/${resources.length} 成功, 耗时 ${totalTime.toFixed(2)}s`);

  // 只有在使用代理时才显示代理池统计
  if (useProxy) {
    const stats = proxyPool.getStats();
    console.log('📊 代理池统计:', stats);
  }

  return results;
}
