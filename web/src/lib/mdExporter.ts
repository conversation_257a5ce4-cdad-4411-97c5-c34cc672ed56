import { AppMsgEx } from '@/types/article';
import { generateExportFilename } from './utils';

export interface MarkdownExportOptions {
  includeMetadata?: boolean;
  imageUrlMode?: 'original' | 'proxy';
  proxyUrl?: string;
}

/**
 * 获取文章标题
 */
function getArticleTitle(document: Document): string {
  const titleElement = document.querySelector('#activity-name, .rich_media_title, h1');
  return titleElement ? titleElement.textContent?.trim() || '微信文章' : '微信文章';
}

/**
 * 获取文章作者
 */
function getArticleAuthor(document: Document): string {
  const authorElement = document.querySelector('#js_name, .rich_media_meta_text, .profile_nickname');
  return authorElement ? authorElement.textContent?.trim() || '' : '';
}

/**
 * 获取发布时间
 */
function getPublishTime(document: Document): string {
  const timeElement = document.querySelector('#publish_time, .rich_media_meta_text');
  if (timeElement) {
    const timeText = timeElement.textContent?.trim() || '';
    const timeMatch = timeText.match(/\d{4}-\d{2}-\d{2}|\d{4}年\d{1,2}月\d{1,2}日/);
    return timeMatch ? timeMatch[0] : '';
  }
  return '';
}

/**
 * 处理图片元素
 */
function processImage(img: HTMLImageElement, options: MarkdownExportOptions = {}): string {
  const urlAttributes = ['data-src', 'data-original', 'data-lazy-src', 'data-url', 'src', 'data-original-src', 'data-actualsrc'];
  let imageUrl = '';
  let usedAttribute = '';

  // 尝试获取图片URL
  for (const attr of urlAttributes) {
    const url = img.getAttribute(attr);
    if (url && url.trim() && !url.startsWith('data:image/svg') && !url.includes('loading')) {
      imageUrl = url.trim();
      usedAttribute = attr;
      break;
    }
  }

  // 如果还是没找到，尝试从父元素或相关元素中查找
  if (!imageUrl) {
    const parent = img.parentElement;
    if (parent) {
      // 检查父元素的背景图片
      const bgImage = window.getComputedStyle(parent).backgroundImage;
      if (bgImage && bgImage !== 'none') {
        const match = bgImage.match(/url\(["']?([^"'\)]+)["']?\)/);
        if (match && match[1]) {
          imageUrl = match[1];
          usedAttribute = 'background-image';
        }
      }

      // 检查父元素的data属性
      if (!imageUrl) {
        for (const attr of urlAttributes) {
          const url = parent.getAttribute(attr);
          if (url && url.trim() && !url.startsWith('data:image/svg')) {
            imageUrl = url.trim();
            usedAttribute = `parent.${attr}`;
            break;
          }
        }
      }
    }
  }

  if (!imageUrl) {
    console.warn('[MD导出] 图片URL获取失败，图片属性:', {
      src: img.getAttribute('src'),
      'data-src': img.getAttribute('data-src'),
      'data-original': img.getAttribute('data-original'),
      className: img.className,
      outerHTML: img.outerHTML.substring(0, 300)
    });
    return '';
  }

  // 根据选项处理图片URL
  if (options.imageUrlMode === 'proxy' && options.proxyUrl) {
    imageUrl = `${options.proxyUrl}/${encodeURIComponent(imageUrl)}`;
  }

  const alt = img.getAttribute('alt') || img.getAttribute('title') || '图片';
  return `![${alt}](${imageUrl})\n\n`;
}

/**
 * 处理链接元素
 */
function processLink(link: HTMLAnchorElement): string {
  const href = link.href;
  const text = link.textContent?.trim() || '';
  return `[${text}](${href})`;
}

/**
 * 处理代码块内容，保留原始格式
 */
function processCodeContent(codeText: string): string {
  return codeText
    .replace(/\r\n/g, '\n')  // 统一换行符为LF
    .replace(/\r/g, '\n')   // 将CR转换为LF
    .replace(/\t/g, '    '); // 将制表符转换为4个空格
}

/**
 * 从代码元素中提取文本，特别处理微信公众号的换行符问题
 */
function extractCodeText(element: HTMLElement): string {
  let codeText = '';
  let hasStructuredContent = false;

  // 递归遍历所有子节点，特别处理br标签和文本节点
  function traverseNode(node: Node, depth = 0) {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent;
      if (text) {
        codeText += text;
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement;
      const tagName = element.tagName.toLowerCase();

      if (tagName === 'br') {
        codeText += '\n';
        hasStructuredContent = true;
      } else if (tagName === 'div' || tagName === 'p') {
        if (codeText && !codeText.endsWith('\n')) {
          codeText += '\n';
        }
        for (const child of element.childNodes) {
          traverseNode(child, depth + 1);
        }
        if (!codeText.endsWith('\n')) {
          codeText += '\n';
        }
        hasStructuredContent = true;
      } else if (tagName === 'span') {
        const className = element.className || '';
        if (className.includes('line') || element.style.display === 'block') {
          if (codeText && !codeText.endsWith('\n')) {
            codeText += '\n';
          }
          for (const child of element.childNodes) {
            traverseNode(child, depth + 1);
          }
          if (!codeText.endsWith('\n')) {
            codeText += '\n';
          }
          hasStructuredContent = true;
        } else {
          for (const child of element.childNodes) {
            traverseNode(child, depth + 1);
          }
        }
      } else {
        for (const child of element.childNodes) {
          traverseNode(child, depth + 1);
        }
      }
    }
  }

  // 开始遍历
  for (const child of element.childNodes) {
    traverseNode(child);
  }

  // 如果没有通过DOM遍历得到有意义的内容，回退到textContent
  if (!codeText.trim() || (!hasStructuredContent && codeText.indexOf('\n') === -1)) {
    const fallbackText = element.textContent || element.innerText || '';

    if (fallbackText.indexOf('\n') === -1) {
      const innerHTML = element.innerHTML;

      if (innerHTML.includes('<br')) {
        const processedHTML = innerHTML
          .replace(/<br\s*\/?>/gi, '\n')
          .replace(/<[^>]+>/g, '')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&');
        codeText = processedHTML;
      } else {
        codeText = fallbackText;
      }
    } else {
      codeText = fallbackText;
    }
  }

  return processCodeContent(codeText);
}

/**
 * 处理表格转换
 */
function processTable(table: HTMLTableElement): string {
  const rows = table.querySelectorAll('tr');
  if (rows.length === 0) return '';

  let markdown = '\n';
  let isFirstRow = true;

  rows.forEach(row => {
    const cells = row.querySelectorAll('th, td');
    if (cells.length === 0) return;

    const cellContents = Array.from(cells).map(cell => {
      return cell.textContent?.trim().replace(/\|/g, '\\|') || '';
    });

    markdown += '| ' + cellContents.join(' | ') + ' |\n';

    if (isFirstRow) {
      const separator = Array.from(cells).map(() => '---').join(' | ');
      markdown += '| ' + separator + ' |\n';
      isFirstRow = false;
    }
  });

  return markdown;
}

/**
 * 转换HTML元素为Markdown
 */
function htmlToMarkdown(element: HTMLElement, options: MarkdownExportOptions = {}): string {
  let markdown = '';

  for (const node of element.childNodes) {
    if (node.nodeType === Node.TEXT_NODE) {
      let text = node.textContent || '';
      
      // 检查父元素链是否包含代码相关的元素
      const isInCodeBlock = (() => {
        let current = node.parentElement;
        while (current) {
          const tagName = current.tagName.toLowerCase();
          const className = current.className || '';

          if (tagName === 'pre' || tagName === 'code') {
            return true;
          }

          if (className.includes('code') ||
              className.includes('highlight') ||
              className.includes('prism') ||
              className.includes('hljs') ||
              className.includes('language-') ||
              className.includes('lang-') ||
              className.includes('prettyprint') ||
              className.includes('syntax')) {
            return true;
          }

          if (current.hasAttribute('data-lang') ||
              current.hasAttribute('data-language') ||
              current.hasAttribute('data-code')) {
            return true;
          }

          current = current.parentElement;
        }
        return false;
      })();

      if (isInCodeBlock) {
        markdown += text;
      } else {
        text = text.replace(/\s+/g, ' ');
        if (node.textContent?.startsWith('\n') || node.textContent?.startsWith('\r')) {
          text = '\n' + text.trim();
        }
        if (node.textContent?.endsWith('\n') || node.textContent?.endsWith('\r')) {
          text = text.trim() + '\n';
        }
        markdown += text;
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement;
      const tagName = element.tagName.toLowerCase();

      switch (tagName) {
        case 'h1':
          markdown += `\n# ${htmlToMarkdown(element, options).trim()}\n\n`;
          break;
        case 'h2':
          markdown += `\n## ${htmlToMarkdown(element, options).trim()}\n\n`;
          break;
        case 'h3':
          markdown += `\n### ${htmlToMarkdown(element, options).trim()}\n\n`;
          break;
        case 'h4':
          markdown += `\n#### ${htmlToMarkdown(element, options).trim()}\n\n`;
          break;
        case 'h5':
          markdown += `\n##### ${htmlToMarkdown(element, options).trim()}\n\n`;
          break;
        case 'h6':
          markdown += `\n###### ${htmlToMarkdown(element, options).trim()}\n\n`;
          break;
        case 'p':
          const pContent = htmlToMarkdown(element, options).trim();
          if (pContent) {
            markdown += `\n\n${pContent}\n\n`;
          }
          break;
        case 'br':
          markdown += '  \n';
          break;
        case 'img':
          markdown += processImage(element as HTMLImageElement, options) + '\n\n';
          break;
        case 'a':
          const linkText = processLink(element as HTMLAnchorElement);
          if (markdown && !markdown.endsWith(' ') && !markdown.endsWith('\n')) {
            markdown += ' ';
          }
          markdown += linkText;
          break;
        case 'strong':
        case 'b':
          markdown += `**${htmlToMarkdown(element, options).trim()}**`;
          break;
        case 'em':
        case 'i':
          markdown += `*${htmlToMarkdown(element, options).trim()}*`;
          break;
        case 'code':
          const inlineCode = element.textContent?.replace(/^\s+|\s+$/g, '') || '';
          markdown += `\`${inlineCode}\``;
          break;
        case 'pre':
          const codeElement = element.querySelector('code');
          if (codeElement) {
            const className = codeElement.className || '';
            const langMatch = className.match(/language-(\w+)/);
            const lang = langMatch ? langMatch[1] : '';
            const codeText = extractCodeText(codeElement);
            markdown += `\n\`\`\`${lang}\n${codeText}\n\`\`\`\n\n`;
          } else {
            const codeText = extractCodeText(element);
            markdown += `\n\`\`\`\n${codeText}\n\`\`\`\n\n`;
          }
          break;
        case 'table':
          markdown += processTable(element as HTMLTableElement) + '\n\n';
          break;
        case 'blockquote':
          const quoteContent = htmlToMarkdown(element, options);
          markdown += `\n> ${quoteContent.trim().replace(/\n/g, '\n> ')}\n\n`;
          break;
        case 'ul':
        case 'ol':
          markdown += '\n';
          const listItems = element.querySelectorAll('li');
          listItems.forEach((li, index) => {
            const prefix = tagName === 'ul' ? '- ' : `${index + 1}. `;
            markdown += `${prefix}${htmlToMarkdown(li, options).trim()}\n`;
          });
          markdown += '\n';
          break;
        case 'hr':
          markdown += '\n---\n\n';
          break;
        case 'del':
        case 's':
          markdown += `~~${htmlToMarkdown(element, options).trim()}~~`;
          break;
        case 'mark':
          markdown += `==${htmlToMarkdown(element, options).trim()}==`;
          break;
        case 'sub':
          markdown += `~${htmlToMarkdown(element, options).trim()}~`;
          break;
        case 'sup':
          markdown += `^${htmlToMarkdown(element, options).trim()}^`;
          break;
        case 'div':
        case 'span':
        case 'section':
          const className = element.className || '';
          
          // 处理代码块容器
          if (className.includes('code') || className.includes('highlight') ||
              className.includes('prism') || className.includes('hljs')) {
            const codeContent = extractCodeText(element);
            if (codeContent.trim()) {
              const langMatch = className.match(/(?:language|lang|hljs)-(\w+)/);
              const lang = langMatch ? langMatch[1] : '';
              markdown += `\n\`\`\`${lang}\n${codeContent}\n\`\`\`\n\n`;
            }
          }
          // 处理引用框
          else if (className.includes('blockquote') ||
                   (className.includes('quote') &&
                    (className.includes('block') || className.includes('box')))) {
            const quoteContent = htmlToMarkdown(element, options);
            if (quoteContent.trim()) {
              markdown += `\n> ${quoteContent.trim().replace(/\n/g, '\n> ')}\n\n`;
            }
          }
          // 检查是否包含图片
          else {
            const images = element.querySelectorAll('img');
            if (images.length > 0) {
              const textContent = element.textContent?.trim() || '';
              if ((element.children.length === 1 && element.children[0].tagName === 'IMG') ||
                  (images.length > 0 && textContent.length < 50)) {
                images.forEach(img => {
                  const imageMarkdown = processImage(img as HTMLImageElement, options);
                  if (imageMarkdown) {
                    markdown += imageMarkdown;
                  }
                });
              } else {
                markdown += htmlToMarkdown(element, options);
              }
            } else {
              markdown += htmlToMarkdown(element, options);
            }
          }
          break;
        default:
          markdown += htmlToMarkdown(element, options);
          break;
      }
    }
  }

  return markdown;
}

/**
 * 清理和格式化Markdown文本
 */
function cleanMarkdown(markdown: string): string {
  const codeBlocks: string[] = [];
  const inlineCodes: string[] = [];
  let codeBlockIndex = 0;
  let inlineCodeIndex = 0;

  // 提取代码块并用占位符替换
  let processedMarkdown = markdown.replace(/```[\s\S]*?```/g, (match) => {
    const placeholder = `__CODE_BLOCK_${codeBlockIndex}__`;
    codeBlocks[codeBlockIndex] = match;
    codeBlockIndex++;
    return placeholder;
  });

  // 提取行内代码并用占位符替换
  processedMarkdown = processedMarkdown.replace(/`[^`\n]+`/g, (match) => {
    const placeholder = `__INLINE_CODE_${inlineCodeIndex}__`;
    inlineCodes[inlineCodeIndex] = match;
    inlineCodeIndex++;
    return placeholder;
  });

  // 对非代码内容进行清理
  processedMarkdown = processedMarkdown
    // 移除多余的空行（超过2个连续换行符）
    .replace(/\n{3,}/g, '\n\n')
    // 移除行首行尾的空格，但保留强制换行的两个空格
    .replace(/^[ \t]+|(?<!  )[ \t]+$/gm, '')
    // 清理多余的空格
    .replace(/ {3,}/g, ' ')
    // 移除重复的图片链接
    .replace(/(![\[^\]]*\]\([^)]+\))\s*\n\s*\1/g, '$1')
    // 移除空白的图片链接
    .replace(/!\[\]\(\s*\)/g, '')
    .replace(/![\[^\]]*\]\(\s*\)/g, '')
    // 修复段落间距
    .replace(/\n\n\n+/g, '\n\n')
    // 确保标题前后有适当的空行
    .replace(/(\n|^)(#{1,6} .+)\n(?!\n)/g, '$1$2\n\n')
    // 移除文档开头的空行
    .replace(/^\n+/g, '')
    // 移除文档结尾的多余空行
    .replace(/\n+$/g, '');

  // 恢复行内代码
  for (let i = 0; i < inlineCodes.length; i++) {
    processedMarkdown = processedMarkdown.replace(`__INLINE_CODE_${i}__`, inlineCodes[i]);
  }

  // 恢复代码块
  for (let i = 0; i < codeBlocks.length; i++) {
    processedMarkdown = processedMarkdown.replace(`__CODE_BLOCK_${i}__`, codeBlocks[i]);
  }

  return processedMarkdown + '\n\n';
}

/**
 * 将HTML内容转换为Markdown格式
 */
export function convertHtmlToMarkdown(
  htmlContent: string,
  article?: AppMsgEx,
  options: MarkdownExportOptions = {}
): string {
  try {
    const parser = new DOMParser();
    const document = parser.parseFromString(htmlContent, 'text/html');

    // 获取文章信息
    const title = article?.title || getArticleTitle(document);
    const author = getArticleAuthor(document);
    const publishTime = getPublishTime(document);

    // 查找文章内容
    const selectors = [
      '#js_content',
      '.rich_media_content',
      '.article-content',
      '[data-role="content"]',
      '.rich_media_area_primary',
      '.rich_media_area_primary_inner',
      '.rich_media_wrp',
      '#page-content',
      '.page-content',
      '.content',
      '.post-content',
      '.article-body',
      '.entry-content',
      'main',
      '[role="main"]'
    ];

    let contentElement: HTMLElement | null = null;

    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLElement;
      if (element && element.textContent && element.textContent.trim().length > 100) {
        contentElement = element;
        break;
      }
    }

    if (!contentElement) {
      throw new Error('未找到文章内容');
    }

    // 构建Markdown内容
    let markdown = '';

    // 添加文章头部信息
    if (options.includeMetadata !== false) {
      markdown += `# ${title}\n\n`;
      if (author) {
        markdown += `**作者：** ${author}\n\n`;
      }
      if (publishTime) {
        markdown += `**发布时间：** ${publishTime}\n\n`;
      }
      if (article?.update_time) {
        const date = new Date(article.update_time * 1000);
        markdown += `**更新时间：** ${date.toLocaleDateString()}\n\n`;
      }
      markdown += '---\n\n';
    }

    // 转换文章内容
    const contentMarkdown = htmlToMarkdown(contentElement, options);
    markdown += contentMarkdown;

    // 清理格式
    return cleanMarkdown(markdown);

  } catch (error) {
    console.error('[MD导出] 转换失败:', error);
    throw error;
  }
}

/**
 * 下载单个Markdown文件
 */
export function downloadMarkdownFile(
  content: string, 
  title: string,
  publishDate?: number,
  accountName?: string,
  itemIdx?: number
): void {
  // 生成安全的文件名
  const filename = generateExportFilename(title, publishDate, accountName, itemIdx);
  
  // 创建Blob对象
  const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
  
  // 创建下载链接
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}.md`;
  
  // 触发下载
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // 释放URL对象
  URL.revokeObjectURL(url);
}
