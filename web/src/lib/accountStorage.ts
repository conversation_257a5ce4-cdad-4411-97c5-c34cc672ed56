// 简化的账号存储操作，用于向后兼容
import { accountStorage, StoredAccount } from '../store/account';
import { Account } from '@/types/account';

export async function getSelectedAccount(userId: string) {
  return accountStorage.getSelectedAccount(userId);
}

export async function setSelectedAccount(
  userId: string,
  account: StoredAccount | null
) {
  if (account === null) {
    return accountStorage.setSelectedAccount(userId, null);
  }
  return accountStorage.saveSelectedAccount(userId, account);
}

export async function addAccountToStorage(account: Account, userId: string) {
  return accountStorage.addAccount(account, userId);
}

export async function getStoredAccounts(userId: string) {
  return accountStorage.getAccountsByUser(userId);
}

export async function removeAccountFromStorage(
  accountFakeid: string,
  userId: string
) {
  return accountStorage.removeAccount(accountFakeid, userId);
}
