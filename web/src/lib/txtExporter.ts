import { generateExportFilename } from './utils';

/**
 * 从HTML元素中提取纯文本内容，保持基本结构
 */
function extractTextFromElement(element: HTMLElement): string {
  let text = '';

  for (const node of element.childNodes) {
    if (node.nodeType === Node.TEXT_NODE) {
      const nodeText = node.textContent || '';
      // 保留必要的空白字符，但清理多余的空白
      const cleanedText = nodeText.replace(/\s+/g, ' ').trim();
      if (cleanedText) {
        text += cleanedText;
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement;
      const tagName = element.tagName.toLowerCase();

      switch (tagName) {
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
          const headingText = extractTextFromElement(element).trim();
          if (headingText) {
            text += `\n\n${headingText}\n\n`;
          }
          break;
        case 'p':
          const pText = extractTextFromElement(element).trim();
          if (pText) {
            text += `\n\n${pText}\n\n`;
          }
          break;
        case 'br':
          text += '\n';
          break;
        case 'div':
        case 'section':
        case 'article':
          const divText = extractTextFromElement(element);
          if (divText.trim()) {
            text += divText;
          }
          break;
        case 'ul':
        case 'ol':
          text += '\n';
          const listItems = element.querySelectorAll('li');
          listItems.forEach((li) => {
            const liText = extractTextFromElement(li).trim();
            if (liText) {
              text += `${liText}\n`;
            }
          });
          text += '\n';
          break;
        case 'blockquote':
          const quoteText = extractTextFromElement(element).trim();
          if (quoteText) {
            text += `\n${quoteText}\n\n`;
          }
          break;
        case 'pre':
        case 'code':
          const codeText = element.textContent || '';
          if (codeText.trim()) {
            text += `\n${codeText}\n`;
          }
          break;
        case 'table':
          text += '\n';
          const rows = element.querySelectorAll('tr');
          rows.forEach(row => {
            const cells = row.querySelectorAll('th, td');
            const cellTexts = Array.from(cells).map(cell => cell.textContent?.trim() || '');
            if (cellTexts.some(text => text)) {
              text += cellTexts.join(' ') + '\n';
            }
          });
          text += '\n';
          break;
        case 'a':
          const linkText = extractTextFromElement(element).trim();
          if (linkText) {
            text += ` ${linkText} `;
          }
          break;
        case 'img':
          // 图片完全忽略，不输出任何内容
          break;
        case 'script':
        case 'style':
          // JavaScript和CSS代码完全忽略，不输出任何内容
          break;
        case 'hr':
          text += '\n\n';
          break;
        case 'strong':
        case 'b':
        case 'em':
        case 'i':
          // 加粗和斜体标签只保留文本内容，不保留格式
          const styledText = extractTextFromElement(element).trim();
          if (styledText) {
            text += ` ${styledText} `;
          }
          break;
        default:
          // 对于其他标签，递归提取文本
          text += extractTextFromElement(element);
          break;
      }
    }
  }

  return text;
}

/**
 * 清理和格式化纯文本内容
 */
function cleanTextContent(text: string): string {
    return text
      // 移除多余的空行（超过2个连续换行符）
      .replace(/\n{3,}/g, '\n\n')
      // 移除行首行尾的空格
      .replace(/^[ \t]+|[ \t]+$/gm, '')
      // 清理多余的空格
      .replace(/ {2,}/g, ' ')
      // 移除文档开头的空行
      .replace(/^\n+/g, '')
      // 确保文档以双换行符结尾
      .replace(/\n*$/, '\n\n');
}

/**
 * 文章类型定义
 */
type ArticleType = 'video_share' | 'audio_share' | 'image_share' | 'text_share' | 'article_share' | 'normal_article' | 'unknown';

/**
 * 检测文章类型
 */
function detectArticleType(document: Document): ArticleType {
    console.log('[TXT导出] 开始检测文章类型...');
    
    // 检测视频/音频分享：查找 #js_common_share_desc_wrap
    if (document.querySelector('#js_common_share_desc_wrap')) {
        console.log('[TXT导出] 检测到视频/音频分享类型');
        return 'video_share';
    }
    
    // 检测图片分享：查找 #js_image_desc
    if (document.querySelector('#js_image_desc')) {
        console.log('[TXT导出] 检测到图片分享类型');
        return 'image_share';
    }
    
    // 检测文本分享：查找 #js_text_desc 且页面包含 text_page_info
    if (document.querySelector('#js_text_desc')) {
        // 进一步验证是否为文本分享页面
        const scriptTags = document.querySelectorAll('script');
        for (const script of scriptTags) {
            if (script.textContent && script.textContent.includes('text_page_info')) {
                console.log('[TXT导出] 检测到文本分享类型');
                return 'text_share';
            }
        }
    }
    
    // 检测文章分享：查找 .original_area_primary
    if (document.querySelector('.original_area_primary')) {
        console.log('[TXT导出] 检测到文章分享类型');
        return 'article_share';
    }
    
    // 检测普通图文：查找 #js_content 且包含 rich_media 相关类名
    const jsContent = document.querySelector('#js_content');
    if (jsContent) {
        const richMediaArea = document.querySelector('.rich_media_area_primary, .rich_media_content');
        if (richMediaArea) {
            console.log('[TXT导出] 检测到普通图文类型');
            return 'normal_article';
        }
    }
    
    console.log('[TXT导出] 未能识别文章类型');
    return 'unknown';
}

/**
 * 根据文章类型获取内容
 */
function getContentByType(document: Document, articleType: ArticleType): { element: HTMLElement | null; text: string } {
    console.log(`[TXT导出] 根据类型 ${articleType} 提取内容...`);
    
    switch (articleType) {
        case 'video_share':
        case 'audio_share':
            // 视频/音频分享：优先从 script 数据中获取，否则从元素获取
            return extractVideoShareContent(document);
            
        case 'image_share':
            // 图片分享：从 #js_image_desc 获取
            return extractImageShareContent(document);
            
        case 'text_share':
            // 文本分享：从 script 数据中的 title 字段获取
            return extractTextShareContent(document);
            
        case 'article_share':
            // 文章分享：从 .original_area_primary 和 #js_content 获取
            return extractArticleShareContent(document);
            
        case 'normal_article':
            // 普通图文：从 #js_content 获取
            return extractNormalArticleContent(document);
            
        default:
            // 未知类型：尝试通用策略
            return extractUnknownContent(document);
    }
}

/**
 * 提取视频/音频分享内容
 */
function extractVideoShareContent(document: Document): { element: HTMLElement | null; text: string } {
    // 优先尝试从 #js_common_share_desc 获取动态内容
    const descElement = document.querySelector('#js_common_share_desc') as HTMLElement;
    if (descElement && descElement.textContent?.trim()) {
        return { element: descElement, text: descElement.textContent.trim() };
    }
    
    // 从 script 数据中提取
    const scriptTags = document.querySelectorAll('script');
    for (const script of scriptTags) {
        const content = script.textContent || '';
        // 查找 desc 变量
        const descMatch = content.match(/desc\s*=\s*['"]([^'"]*)['"]/);
        if (descMatch && descMatch[1]) {
            return { element: null, text: descMatch[1] };
        }
    }
    
    // fallback 到容器元素
    const wrapElement = document.querySelector('#js_common_share_desc_wrap') as HTMLElement;
    if (wrapElement && wrapElement.textContent?.trim()) {
        return { element: wrapElement, text: wrapElement.textContent.trim() };
    }
    
    return { element: null, text: '' };
}

/**
 * 提取图片分享内容
 */
function extractImageShareContent(document: Document): { element: HTMLElement | null; text: string } {
    // 从整个HTML字符串中按照内容完整性优先级提取
    const htmlString = document.documentElement.outerHTML || '';

    // 优先级1: ContentNoEncode（通常包含完整内容）- 精确匹配
    const contentNoEncodeMatch = htmlString.match(/var\s+ContentNoEncode\s*=\s*[^'"]*['"]([^'"]*?)['"]/) ||
                                htmlString.match(/ContentNoEncode\s*=\s*[^'"]*['"]([^'"]*?)['"](?=\s*;)/);
    if (contentNoEncodeMatch && contentNoEncodeMatch[1]) {
        const text = contentNoEncodeMatch[1]
            .replace(/\\x0a/g, '\n')
            .replace(/\\'/g, "'")
            .replace(/\\\\/g, '\\')
            .trim();
        if (text && text.length > 10) {
            console.log('[TXT导出] 图片分享使用ContentNoEncode提取');
            return { element: null, text };
        }
    }

    // 优先级2: window.desc（图片分享的官方首选）
    const windowDescMatch = htmlString.match(/window\.desc\s*=\s*['"]([^'"]*?)['"](?=\.replace)/);
    if (windowDescMatch && windowDescMatch[1]) {
        const text = windowDescMatch[1]
            .replace(/\\x0a/g, '\n')
            .replace(/\\'/g, "'")
            .replace(/\\\\/g, '\\')
            .trim();
        if (text && text.length > 10) {
            console.log('[TXT导出] 图片分享使用window.desc提取');
            return { element: null, text };
        }
    }

    // 优先级3: 独立的 desc 变量（不在 window 对象上）
    const descVarMatch = htmlString.match(/var\s+desc\s*=\s*[^'"]*['"]([^'"]*?)['"](?=\s*;)/) ||
                        htmlString.match(/(?<!window\.)(?<!\w)desc\s*=\s*[^'"]*['"]([^'"]*?)['"](?=\s*;)/);
    if (descVarMatch && descVarMatch[1]) {
        const text = descVarMatch[1]
            .replace(/\\x0a/g, '\n')
            .replace(/\\'/g, "'")
            .replace(/\\\\/g, '\\')
            .trim();
        if (text && text.length > 10) {
            console.log('[TXT导出] 图片分享使用desc变量提取');
            return { element: null, text };
        }
    }

    // 优先级4: content变量（备选方案）
    const contentMatch = htmlString.match(/var\s+content\s*=\s*[^'"]*['"]([^'"]*?)['"](?=\s*;)/) ||
                        htmlString.match(/(?<!\w)content\s*=\s*[^'"]*['"]([^'"]*?)['"](?=\s*;)/);
    if (contentMatch && contentMatch[1]) {
        const text = contentMatch[1]
            .replace(/\\x0a/g, '\n')
            .replace(/\\'/g, "'")
            .replace(/\\\\/g, '\\')
            .trim();
        if (text && text.length > 10) {
            console.log('[TXT导出] 图片分享使用content变量提取');
            return { element: null, text };
        }
    }

    // 优先级5: 从 meta 标签的 og:description 提取
    const ogDescMatch = htmlString.match(/<meta\s+property="og:description"\s+content="([^"]*?)"/);
    if (ogDescMatch && ogDescMatch[1]) {
        const text = ogDescMatch[1]
            .replace(/\\x0a/g, '\n')
            .replace(/&#x0A;/g, '\n')
            .replace(/&quot;/g, '"')
            .replace(/&amp;/g, '&')
            .trim();
        if (text && text.length > 10) {
            console.log('[TXT导出] 图片分享使用meta description提取');
            return { element: null, text };
        }
    }

    // 优先级6: 从 __QMTPL_SSR_DATA__ 中的 desc 字段提取
    const ssrDescMatch = htmlString.match(/desc\s*:\s*['"]([^'"]*?)['"](?=\s*[,}])/);
    if (ssrDescMatch && ssrDescMatch[1]) {
        const text = ssrDescMatch[1]
            .replace(/\\x0a/g, '\n')
            .replace(/\\'/g, "'")
            .replace(/\\\\/g, '\\')
            .trim();
        if (text && text.length > 10) {
            console.log('[TXT导出] 图片分享使用SSR desc字段提取');
            return { element: null, text };
        }
    }

    // 备用方法：从其他JavaScript变量提取
    const fallbackMethods = [
        { pattern: /window\.msg_title\s*=\s*['"]([^'"]*?)['"]/, name: 'window.msg_title' },
        { pattern: /var\s+title\s*=\s*['"]([^'"]*?)['"]/, name: 'var title' },
        { pattern: /title\s*:\s*['"]([^'"]*?)['"]/, name: 'SSR title' }
    ];

    for (const method of fallbackMethods) {
        const match = htmlString.match(method.pattern);
        if (match && match[1]) {
            const text = match[1]
                .replace(/\\x0a/g, '\n')
                .replace(/\\'/g, "'")
                .replace(/\\\\/g, '\\')
                .trim();
            if (text && text.length > 10) {
                console.log(`[TXT导出] 图片分享使用${method.name}提取`);
                return { element: null, text };
            }
        }
    }

    return { element: null, text: '' };
}

/**
 * 提取文本分享内容
 */
function extractTextShareContent(document: Document): { element: HTMLElement | null; text: string } {
    // 从整个HTML字符串中按照微信的实际优先级顺序提取
    const htmlString = document.documentElement.outerHTML || '';
    
    // 优先级1: ContentNoEncode（文本分享时的首选）
    const contentNoEncodeMatch = htmlString.match(/ContentNoEncode\s*=\s*[^']*?['"]([^'"]*?)['"]/);
    if (contentNoEncodeMatch && contentNoEncodeMatch[1]) {
        const text = contentNoEncodeMatch[1]
            .replace(/\\x0a/g, '\n')
            .replace(/\\'/g, "'")
            .replace(/\\\\/g, '\\')
            .trim();
        if (text && text.length > 10) {
            console.log('[TXT导出] 文本分享使用ContentNoEncode提取');
            return { element: null, text };
        }
    }
    
    // 优先级2: TextContentNoEncode（备选方案）
    const textContentMatch = htmlString.match(/TextContentNoEncode\s*=\s*[^']*?['"]([^'"]*?)['"]/);
    if (textContentMatch && textContentMatch[1]) {
        const text = textContentMatch[1]
            .replace(/\\x0a/g, '\n')
            .replace(/\\'/g, "'")
            .replace(/\\\\/g, '\\')
            .trim();
        if (text && text.length > 10) {
            console.log('[TXT导出] 文本分享使用TextContentNoEncode提取');
            return { element: null, text };
        }
    }
    
    // 优先级3: desc变量（最后备选）
    const descMatch = htmlString.match(/(?:var\s+desc\s*=|desc\s*=)\s*['"]([^'"]*?)['"]/);
    if (descMatch && descMatch[1]) {
        const text = descMatch[1]
            .replace(/\\x0a/g, '\n')
            .replace(/\\'/g, "'")
            .replace(/\\\\/g, '\\')
            .trim();
        if (text && text.length > 10) {
            console.log('[TXT导出] 文本分享使用desc变量提取');
            return { element: null, text };
        }
    }
    
    // 备用方法：从其他JavaScript变量提取
    const fallbackMethods = [
        { pattern: /window\.msg_title\s*=\s*['"]([^'"]*?)['"]/, name: 'window.msg_title' },
        { pattern: /var\s+title\s*=\s*['"]([^'"]*?)['"]/, name: 'var title' },
        { pattern: /d\.title\s*=\s*[^:]*?:\s*['"]([^'"]*?)['"]/, name: 'd.title' },
        { pattern: /title\s*:\s*['"]([^'"]*?)['"]/, name: 'SSR title' }
    ];
    
    for (const method of fallbackMethods) {
        const match = htmlString.match(method.pattern);
        if (match && match[1]) {
            const text = match[1]
                .replace(/\\x0a/g, '\n')
                .replace(/\\'/g, "'")
                .replace(/\\\\/g, '\\')
                .trim();
            if (text && text.length > 10) {
                console.log(`[TXT导出] 文本分享使用${method.name}提取`);
                return { element: null, text };
            }
        }
    }
    
    // 最后尝试meta标签
    const ogTitleMatch = htmlString.match(/<meta\s+property="og:title"\s+content="([^"]*?)"/);
    if (ogTitleMatch && ogTitleMatch[1]) {
        const text = ogTitleMatch[1]
            .replace(/&#x0A;/g, '\n')
            .replace(/&quot;/g, '"')
            .replace(/&amp;/g, '&')
            .trim();
        if (text && text.length > 50 && text.includes('\n')) {
            console.log('[TXT导出] 文本分享使用meta标签提取');
            return { element: null, text };
        }
    }
    
    return { element: null, text: '' };
}

/**
 * 提取文章分享内容
 */
function extractArticleShareContent(document: Document): { element: HTMLElement | null; text: string } {
    const originalArea = document.querySelector('.original_area_primary') as HTMLElement;
    const jsContent = document.querySelector('#js_content') as HTMLElement;
    
    let text = '';
    let element: HTMLElement | null = null;
    
    // 获取编者荐语
    if (originalArea && originalArea.textContent?.trim()) {
        text += '编者荐语：\n' + originalArea.textContent.trim() + '\n\n';
        element = originalArea;
    }
    
    // 获取正文内容
    if (jsContent && jsContent.textContent?.trim()) {
        text += jsContent.textContent.trim();
        element = element || jsContent;
    }
    
    return { element, text: text.trim() };
}

/**
 * 提取普通图文内容
 */
function extractNormalArticleContent(document: Document): { element: HTMLElement | null; text: string } {
    // 尝试多种选择器
    const selectors = [
        '#js_content .rich_media_content',
        '#js_content',
        '.rich_media_content',
        '.rich_media_area_primary #js_content',
        '.rich_media_area_primary_inner'
    ];
    
    for (const selector of selectors) {
        const element = document.querySelector(selector) as HTMLElement;
        if (element && element.textContent?.trim() && element.textContent.trim().length > 100) {
            return { element, text: element.textContent.trim() };
        }
    }
    
    return { element: null, text: '' };
}

/**
 * 提取未知类型内容
 */
function extractUnknownContent(document: Document): { element: HTMLElement | null; text: string } {
    console.log('[TXT导出] 使用通用策略提取内容...');
    
    // 尝试常见的内容选择器
    const selectors = [
        '#js_content',
        '.rich_media_content',
        '.article-content', 
        '[data-role="content"]',
        '.rich_media_area_primary',
        '.rich_media_area_primary_inner',
        'main',
        '[role="main"]'
    ];

    for (const selector of selectors) {
        const element = document.querySelector(selector) as HTMLElement;
        if (element && element.textContent?.trim() && element.textContent.trim().length > 50) {
            console.log(`[TXT导出] 通用策略使用选择器 "${selector}" 找到内容`);
            return { element, text: element.textContent.trim() };
        }
    }

    // 智能查找最大内容容器
    const candidates = document.querySelectorAll('div, section, article, main') as NodeListOf<HTMLElement>;
    let bestCandidate: HTMLElement | null = null;
    let maxLength = 0;

    for (const candidate of candidates) {
        const textLength = candidate.textContent?.trim().length || 0;
        if (textLength > 200 && textLength > maxLength) {
            const className = candidate.className || '';
            const id = candidate.id || '';
            if (!className.includes('nav') && !className.includes('header') &&
                !className.includes('footer') && !className.includes('sidebar') &&
                !id.includes('nav') && !id.includes('header') && !id.includes('footer')) {
                bestCandidate = candidate;
                maxLength = textLength;
            }
        }
    }

    if (bestCandidate) {
        console.log(`[TXT导出] 智能查找找到内容容器，长度: ${maxLength}`);
        return { element: bestCandidate, text: bestCandidate.textContent?.trim() || '' };
    }

    return { element: null, text: '' };
}

/**
 * 将HTML内容转换为纯文本格式
 */
export function convertHtmlToText(
  htmlContent: string,
): string {
    const parser = new DOMParser();
    const document = parser.parseFromString(htmlContent, 'text/html');

    // 检测文章类型
    const articleType = detectArticleType(document);
    console.log(`[TXT导出] 检测到文章类型: ${articleType}`);
    
    // 根据类型获取内容
    const { element, text } = getContentByType(document, articleType);
    
    // 验证内容是否获取成功
    if (!text || text.trim().length < 5) {
        throw new Error(`未找到 ${articleType} 类型的文章内容`);
    }

    console.log(`[TXT导出] 成功提取 ${articleType} 类型内容，长度: ${text.length}`);
    
    // 如果有HTML元素，尝试进行更精细的文本提取
    let finalText = text;
    if (element) {
        try {
            // 移除script和style标签及其内容
            const scriptsAndStyles = document.querySelectorAll('script, style');
            scriptsAndStyles.forEach(el => el.remove());
            
            const extractedText = extractTextFromElement(element);
            if (extractedText.trim().length > text.length * 0.5) {
                // 如果提取的结构化文本长度合理，使用它
                finalText = extractedText;
            }
        } catch (e) {
            console.log('[TXT导出] 结构化文本提取失败，使用原始文本:', e);
        }
    }

    // 清理格式并返回
    return cleanTextContent(finalText.trim());
}

/**
 * 下载单个TXT文件
 */
export function downloadTextFile(
  content: string, 
  title: string,
  publishDate?: number,
  accountName?: string,
  itemIdx?: number
): void {
  // 生成安全的文件名
  const filename = generateExportFilename(title, publishDate, accountName, itemIdx);
  
  // 创建Blob对象
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
  
  // 创建下载链接
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}.txt`;
  
  // 触发下载
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // 释放URL对象
  URL.revokeObjectURL(url);
}
