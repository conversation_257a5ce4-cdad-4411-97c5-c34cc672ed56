import { User, ApiResponse } from '@/types';
import {
  AppMsgEx,
  AppMsgPublishResponse,
  WechatApiArticle,
} from '@/types/article';
import { clearAuthData } from '@/lib/auth';
import { Account } from '@/types/account';

// 优先使用：
// - NEXT_PUBLIC_API_BASE（完整路径，如 http://host:port/api/v1）
// - 其次使用 NEXT_PUBLIC_API_BASE_URL（仅主机，如 http://host:port），自动补全 /api/v1
// - 最后默认 http://localhost:8080/api/v1
const API_BASE = (() => {
  const legacyFull = (process.env.NEXT_PUBLIC_API_BASE || '').trim();
  const baseUrl = (process.env.NEXT_PUBLIC_API_BASE_URL || '').trim();

  if (legacyFull) return legacyFull;
  if (baseUrl) return `${baseUrl.replace(/\/$/, '')}/api/v1`;
  return 'http://localhost:8080/api/v1';
})();

class WechatApi {
  // 将微信消息的文章转换为扁平的文章数组
  private transformWechatResponse(
    response: AppMsgPublishResponse
  ): WechatApiArticle {
    const articles: AppMsgEx[] = [];
    if (response.publish_page?.publish_list) {
      response.publish_page.publish_list.flatMap((publishItem) => {
        if (
          publishItem.publish_info?.appmsgex &&
          Array.isArray(publishItem.publish_info.appmsgex)
        ) {
          articles.push(
            ...publishItem.publish_info.appmsgex.map((item) => ({
              ...item,
              appmsg_album_titles:
                item.appmsg_album_infos?.map((info) => info.title) ?? [],
            }))
          );
        }
      });
    }
    return {
      total_message_count: response.publish_page?.total_count || 0,
      articles: articles,
    };
  }

  private async request<T>(
    endpoint: string,
    options?: RequestInit
  ): Promise<T> {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      credentials: 'include',
      ...options,
    });

    if (!response.ok) {
      // 如果是401错误，说明token过期，清除用户数据
      if (response.status === 401) {
        clearAuthData();
        if (typeof window !== 'undefined') {
          window.location.reload();
        }
      }

      // 尝试获取错误响应体的详细信息
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorResponse = await response.json();
        if (errorResponse.message) {
          errorMessage = errorResponse.message;
        }
      } catch {
        // 如果解析JSON失败，使用默认错误消息
      }

      throw new Error(errorMessage);
    }

    const result: ApiResponse<T> = await response.json();
    if (result.code !== 200) {
      throw new Error(result.message || 'Request failed');
    }

    return result.data;
  }

  async startSession(): Promise<string> {
    const response = await fetch(`${API_BASE}/wechat/session`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      let errorMessage = `Session API failed with status: ${response.status}`;
      try {
        const errorResponse = await response.json();
        if (errorResponse.message) {
          errorMessage = errorResponse.message;
        }
      } catch {
        // 如果解析JSON失败，使用默认错误消息
      }
      throw new Error(errorMessage);
    }

    return response.text();
  }

  async getQRCode(): Promise<Blob> {
    const response = await fetch(`${API_BASE}/wechat/qrcode`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      let errorMessage = `QRCode API failed with status: ${response.status}`;
      try {
        const errorResponse = await response.json();
        if (errorResponse.message) {
          errorMessage = errorResponse.message;
        }
      } catch {
        // 如果解析JSON失败，使用默认错误消息
      }
      throw new Error(errorMessage);
    }

    return response.blob();
  }

  async checkScanStatus(): Promise<{
    status: string;
    acct_size?: number;
    binduin?: boolean;
  }> {
    return this.request('/wechat/scan');
  }

  async login(): Promise<User> {
    return this.request<User>('/wechat/login');
  }

  async searchAccounts(
    keyword: string,
    token: string,
    begin = '0',
    count = '20'
  ): Promise<{ list: Array<Account> }> {
    return this.request(
      `/wechat/search-account?keyword=${encodeURIComponent(keyword)}&token=${encodeURIComponent(token)}&begin=${begin}&count=${count}`
    );
  }

  async searchArticles(
    fakeid: string,
    token: string,
    keyword = '',
    begin = '0',
    count = '20'
  ): Promise<WechatApiArticle> {
    const wechatResponse = await this.request<AppMsgPublishResponse>(
      `/wechat/search-article?fakeid=${fakeid}&token=${encodeURIComponent(token)}&keyword=${encodeURIComponent(keyword)}&begin=${begin}&count=${count}`
    );
    return this.transformWechatResponse(wechatResponse);
  }
}

export const wechatApi = new WechatApi();
