'use client';

import { useState, useEffect, useCallback } from 'react';
import { useApp } from '@/contexts/AppContext';
import { wechatApi } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/toast';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function LoginModal({ isOpen, onClose }: LoginModalProps) {
  const { state, dispatch } = useApp();
  const [qrCodeBlob, setQrCodeBlob] = useState<string | null>(null);
  const [scanInterval, setScanInterval] = useState<NodeJS.Timeout | null>(null);
  const [scanStatus, setScanStatus] = useState(0); // 0: 等待扫码, 1: 扫码成功(1个账号), 2: 扫码成功(多个账号), 3: 没有可登录账号, 4: 登录失败, 5: 二维码已过期, 6: 二维码加载失败, 7: 需要绑定邮箱, 8: Session API 失败, 9: QRCode API 失败
  const [qrcodeRefreshTimes, setQrcodeRefreshTimes] = useState(0);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [qrCodeExpireTime, setQrCodeExpireTime] = useState<Date | null>(null);
  const [expireCountdown, setExpireCountdown] = useState(180); // 默认3分钟倒计时
  const [frozenCountdown, setFrozenCountdown] = useState<number | null>(null); // 扫码时冻结的倒计时
  const [loginError, setLoginError] = useState<string | null>(null); // 存储登录错误信息
  const [isRefreshing, setIsRefreshing] = useState(false); // 刷新状态

  // 监听扫码状态变化，只对错误状态显示 toast 消息
  useEffect(() => {
    switch (scanStatus) {
      case 3:
        toast.warning('没有可登录账号', '该微信还未注册公众号');
        break;
      case 4:
        toast.error('登录失败', loginError || '请重新扫码');
        break;
      case 5:
        toast.warning('二维码已过期', '请点击重新扫码按钮获取新的二维码');
        break;
      case 6:
        toast.error('二维码加载失败', '请重试');
        break;
      case 8:
        toast.error(
          '登录会话初始化失败',
          loginError || '无法启动登录会话，请重试'
        );
        break;
      case 9:
        toast.error(
          '二维码获取失败',
          loginError || '无法获取登录二维码，请重试'
        );
        break;
      // 成功状态(1,2,7)和等待状态(0)不显示 toast，保持原有的遮罩显示
    }
  }, [scanStatus, loginError]);

  // 当模态框打开时，自动开始登录流程
  useEffect(() => {
    if (isOpen && !state.user) {
      startLogin();
    }
  }, [isOpen, state.user]); // startLogin 在组件内定义，依赖会变化

  // 当用户登录成功后，关闭模态框
  useEffect(() => {
    if (state.user && isOpen) {
      onClose();
    }
  }, [state.user, isOpen, onClose]);

  // 二维码过期倒计时
  useEffect(() => {
    if (!qrCodeExpireTime) return;

    const countdownInterval = setInterval(() => {
      // 如果已经扫码成功，不再更新倒计时
      if (scanStatus === 1 || scanStatus === 2 || scanStatus === 7) {
        if (frozenCountdown === null) {
          setFrozenCountdown(expireCountdown);
        }
        return;
      }

      const now = new Date().getTime();
      const expireTime = qrCodeExpireTime.getTime();
      const timeLeft = expireTime - now;

      if (timeLeft <= 0) {
        setExpireCountdown(0);
        setScanStatus(5); // 设置为过期状态
        clearInterval(countdownInterval);
      } else {
        setExpireCountdown(Math.ceil(timeLeft / 1000)); // 转换为秒
      }
    }, 1000);

    return () => clearInterval(countdownInterval);
  }, [qrCodeExpireTime, scanStatus, expireCountdown, frozenCountdown]);

  // 清理资源
  useEffect(() => {
    return () => {
      stopPolling();
      if (qrCodeBlob) {
        URL.revokeObjectURL(qrCodeBlob);
      }
    };
  }, []);

  // 当模态框关闭时清理状态
  useEffect(() => {
    if (!isOpen) {
      stopPolling();
      if (qrCodeBlob) {
        URL.revokeObjectURL(qrCodeBlob);
        setQrCodeBlob(null);
      }
      setScanStatus(0);
      setQrcodeRefreshTimes(0);
      setIsLoggingIn(false);
      setQrCodeExpireTime(null);
      setExpireCountdown(180); // 重置为3分钟
      setFrozenCountdown(null);
      setLoginError(null); // 重置登录错误信息
      setIsRefreshing(false); // 重置刷新状态
      dispatch({ type: 'SET_QRCODE', payload: null });
      dispatch({ type: 'SET_ERROR', payload: null });
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [isOpen]);

  const getScanStatusInfo = () => {
    switch (scanStatus) {
      case 0:
        return { desc: '请使用微信扫描二维码登录', showMask: false };
      case 1:
        return {
          desc: isLoggingIn
            ? '正在登录中，请稍候...'
            : '请在微信中确认账号登录',
          showMask: true,
          maskText: isLoggingIn ? '登录中...' : '扫码成功',
          maskType: 'success',
        };
      case 2:
        return {
          desc: isLoggingIn
            ? '正在登录中，请稍候...'
            : '请在微信中选择账号登录',
          showMask: true,
          maskText: isLoggingIn ? '登录中...' : '扫码成功',
          maskType: 'success',
        };
      case 3:
        return {
          desc: '该微信还未注册公众号',
          showMask: true,
          maskText: '没有可登录账号',
          maskType: 'warning',
        };
      case 4:
        return {
          desc: '登录失败，请重新扫码登录',
          showMask: true,
          maskText: '登录失败',
          maskType: 'error',
        };
      case 5:
        return {
          desc: '请点击重新扫码按钮获取新的二维码',
          showMask: true,
          maskText: '二维码已过期',
          maskType: 'warning',
        };
      case 6:
        return {
          desc: '二维码加载失败，请重试',
          showMask: true,
          maskText: '二维码加载失败',
          maskType: 'error',
        };
      case 7:
        return {
          desc: isLoggingIn ? '正在登录中，请稍候...' : '该账号尚未绑定邮箱',
          showMask: true,
          maskText: isLoggingIn ? '登录中...' : '扫码成功',
          maskType: 'success',
        };
      case 8:
        return {
          desc: '登录会话初始化失败，请重试',
          showMask: true,
          maskText: '初始化登录会话失败',
          maskType: 'error',
        };
      case 9:
        return {
          desc: '二维码获取失败，请重试',
          showMask: true,
          maskText: '获取二维码失败',
          maskType: 'error',
        };
      default:
        return { desc: '请使用微信扫描二维码登录', showMask: false };
    }
  };

  // 格式化倒计时显示
  const formatCountdown = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // 公共的登录初始化逻辑
  const initializeLogin = async () => {
    // 先调用 session API
    try {
      await wechatApi.startSession();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Session 初始化失败';
      setLoginError(errorMessage);
      setScanStatus(8); // Session API 失败
      return false;
    }

    // session 成功后，再调用 qrcode API
    try {
      const blob = await wechatApi.getQRCode();
      const url = URL.createObjectURL(blob);
      setQrCodeBlob(url);
      dispatch({ type: 'SET_QRCODE', payload: url });

      // 设置二维码3分钟后过期
      const expireTime = new Date();
      expireTime.setMinutes(expireTime.getMinutes() + 3);
      setQrCodeExpireTime(expireTime);
      setExpireCountdown(180); // 重置倒计时
      setFrozenCountdown(null); // 清除冻结状态

      startPolling();
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : '二维码获取失败';
      setLoginError(errorMessage);
      setScanStatus(9); // QRCode API 失败
      return false;
    }
  };

  const startLogin = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });
      setScanStatus(0);
      setQrcodeRefreshTimes(0);
      setLoginError(null); // 重置登录错误信息

      await initializeLogin();
    } catch (error) {
      // 兜底错误处理
      const errorMessage =
        error instanceof Error ? error.message : '登录初始化失败';
      setLoginError(errorMessage);
      setScanStatus(6);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const refreshQrcode = async () => {
    if (isRefreshing) {
      return; // 防止重复点击
    }

    setIsRefreshing(true);
    stopPolling();

    try {
      // 如果达到最大刷新次数，重置次数并允许重新开始
      if (qrcodeRefreshTimes >= 5) {
        setQrcodeRefreshTimes(0); // 重置刷新次数
        setScanStatus(0); // 重置为等待状态而不是过期状态
      } else {
        setScanStatus(0);
        setQrcodeRefreshTimes((prev) => prev + 1);
      }

      dispatch({ type: 'SET_ERROR', payload: null }); // 清除错误状态
      setLoginError(null); // 重置登录错误信息

      await initializeLogin();
    } catch (error) {
      console.error('刷新二维码失败:', error);
      const errorMessage =
        error instanceof Error ? error.message : '刷新二维码失败';
      setLoginError(errorMessage);
      setScanStatus(6);
    } finally {
      setIsRefreshing(false);
    }
  };

  const startPolling = () => {
    if (scanInterval) {
      clearInterval(scanInterval);
    }

    const interval = setInterval(async () => {
      try {
        const status = await wechatApi.checkScanStatus();
        const statusCode = parseInt(status.status || '0');

        switch (statusCode) {
          case 1:
            if (!isLoggingIn) {
              clearInterval(interval);
              setScanInterval(null);
              await completeLogin();
            }
            break;
          case 2:
            await refreshQrcode();
            break;
          case 3:
            await refreshQrcode();
            break;
          case 4:
            if (status.acct_size === 1) {
              setScanStatus(1);
            } else if ((status.acct_size ?? 0) > 1) {
              setScanStatus(2);
            } else {
              setScanStatus(3);
            }
            break;
          case 5:
            if (status.binduin) {
              setScanStatus(7);
            } else {
              setScanStatus(4);
            }
            break;
          default:
            break;
        }
      } catch (error) {
        console.error('检查扫码状态失败:', error);
        setScanStatus(6);
        clearInterval(interval);
      }
    }, 1500);

    setScanInterval(interval);
  };

  const stopPolling = useCallback(() => {
    if (scanInterval) {
      clearInterval(scanInterval);
      setScanInterval(null);
    }
  }, [scanInterval]);

  const completeLogin = async () => {
    if (isLoggingIn) {
      return;
    }

    try {
      setIsLoggingIn(true);
      // 不设置全局 loading 状态，避免二维码显示加载动画

      const user = await wechatApi.login();
      dispatch({ type: 'SET_USER', payload: user });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录失败';
      setLoginError(errorMessage);
      setScanStatus(4);
    } finally {
      // 不重置全局 loading 状态
      setIsLoggingIn(false);
    }
  };

  if (!isOpen) return null;

  const statusInfo = getScanStatusInfo();

  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black/20 backdrop-blur-lg"
        style={{ backdropFilter: 'blur(12px)' }}
        onClick={onClose}
      ></div>

      {/* 模态框内容 */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 p-6">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors cursor-pointer"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        {/* 标题 */}
        <div className="text-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">登录微信公众号</h2>
        </div>

        {/* 错误提示 */}
        {state.error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
            {state.error}
          </div>
        )}

        {/* 二维码区域 */}
        <div className="text-center">
          {state.isLoading ? (
            <div className="flex items-center justify-center h-48">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : qrCodeBlob ? (
            <div className="relative">
              {/* 二维码容器 */}
              <div className="mb-6 flex justify-center relative">
                <div className="inline-block p-4 bg-white rounded-lg border">
                  <img
                    src={qrCodeBlob}
                    alt="微信二维码"
                    className="w-48 h-48"
                  />
                </div>

                {/* 遮罩层 - 只覆盖二维码图片 */}
                {statusInfo.showMask && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-sm rounded-lg">
                    <div className="text-center p-6">
                      {statusInfo.maskType === 'success' && (
                        <div
                          className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center border ${
                            isLoggingIn
                              ? 'bg-blue-50 border-blue-200'
                              : 'bg-green-50 border-green-200'
                          }`}
                        >
                          {isLoggingIn ? (
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                          ) : (
                            <svg
                              className="w-8 h-8 text-green-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                          )}
                        </div>
                      )}
                      {statusInfo.maskType === 'warning' && (
                        <div className="w-16 h-16 mx-auto mb-4 bg-yellow-50 rounded-full flex items-center justify-center border border-yellow-200">
                          <svg
                            className="w-8 h-8 text-yellow-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                            />
                          </svg>
                        </div>
                      )}
                      {statusInfo.maskType === 'error' && (
                        <div className="w-16 h-16 mx-auto mb-4 bg-red-50 rounded-full flex items-center justify-center border border-red-200">
                          <svg
                            className="w-8 h-8 text-red-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </div>
                      )}
                      <p className="text-base font-semibold text-gray-800 mb-2">
                        {statusInfo.maskText}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* 状态说明 */}
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">{statusInfo.desc}</p>

                {/* 倒计时显示 - 扫码成功后简化显示 */}
                <div className="flex items-center justify-center space-x-3 mb-3">
                  {scanStatus === 1 || scanStatus === 2 || scanStatus === 7 ? (
                    // 扫码成功状态 - 根据是否正在登录显示不同内容
                    <p className="text-xs text-gray-500">
                      {isLoggingIn
                        ? '登录中...'
                        : '扫码成功，请在微信中完成操作'}
                    </p>
                  ) : (
                    // 其他状态 - 正常显示倒计时
                    <>
                      <div className="text-xs text-gray-500 min-w-[120px] text-center">
                        {scanStatus === 5 ? (
                          <span className="text-yellow-600 font-medium">
                            二维码已过期
                          </span>
                        ) : scanStatus === 4 ? (
                          <span className="text-red-600 font-medium">
                            登录失败
                          </span>
                        ) : scanStatus === 8 ? (
                          <span className="text-red-600 font-medium">
                            登录会话初始化失败
                          </span>
                        ) : scanStatus === 9 ? (
                          <span className="text-red-600 font-medium">
                            二维码获取失败
                          </span>
                        ) : (
                          <>
                            二维码将在{' '}
                            <span className="text-blue-600 font-medium">
                              {formatCountdown(
                                frozenCountdown !== null
                                  ? frozenCountdown
                                  : expireCountdown
                              )}
                            </span>{' '}
                            后过期
                          </>
                        )}
                      </div>
                      {scanStatus === 0 ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={refreshQrcode}
                          disabled={isRefreshing}
                          className="text-blue-600 border-blue-300 hover:text-blue-600 hover:bg-blue-50 hover:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer transition-all text-xs px-1.5 py-0 h-6 flex items-center gap-1"
                        >
                          <svg
                            className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                            />
                          </svg>
                          刷新
                        </Button>
                      ) : scanStatus === 5 ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={refreshQrcode}
                          disabled={isRefreshing}
                          className="text-yellow-600 border-yellow-300 hover:text-yellow-600 hover:bg-yellow-50 hover:border-yellow-400 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer transition-all text-xs px-1.5 py-0 h-6 flex items-center gap-1"
                        >
                          <svg
                            className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                            />
                          </svg>
                          重新扫码
                        </Button>
                      ) : (
                        (scanStatus === 3 ||
                          scanStatus === 4 ||
                          scanStatus === 6 ||
                          scanStatus === 8 ||
                          scanStatus === 9) && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={refreshQrcode}
                            disabled={isRefreshing}
                            className="text-red-600 border-red-300 hover:text-red-600 hover:bg-red-50 hover:border-red-400 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer transition-all text-xs px-1.5 py-0 h-6 flex items-center gap-1"
                          >
                            <svg
                              className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                              />
                            </svg>
                            重试
                          </Button>
                        )
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          ) : (
            // 如果没有二维码且不在加载中，显示错误状态
            <div className="h-48 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-red-50 rounded-full flex items-center justify-center border border-red-200">
                  <svg
                    className="w-8 h-8 text-red-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
                <p className="text-base font-semibold text-gray-800 mb-2">
                  登录会话初始化失败
                </p>
                <p className="text-sm text-gray-600 mb-4">请刷新页面重试</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
