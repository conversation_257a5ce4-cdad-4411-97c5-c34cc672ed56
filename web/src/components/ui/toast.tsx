'use client';

import { useState, useEffect } from 'react';
import { X, CheckCircle, AlertCircle, XCircle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastProps {
  id: string;
  type: ToastType;
  title: string;
  description?: string;
  duration?: number;
  onClose: (id: string) => void;
}

const toastIcons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertCircle,
  info: Info,
};

const toastStyles = {
  success: 'bg-green-50 border-green-200 text-green-800',
  error: 'bg-red-50 border-red-200 text-red-800',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  info: 'bg-blue-50 border-blue-200 text-blue-800',
};

const toastIconColors = {
  success: 'text-green-500',
  error: 'text-red-500',
  warning: 'text-yellow-500',
  info: 'text-blue-500',
};

export function Toast({
  id,
  type,
  title,
  description,
  duration = 4000,
  onClose,
}: ToastProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  const Icon = toastIcons[type];

  useEffect(() => {
    // 延迟显示动画
    requestAnimationFrame(() => {
      setIsVisible(true);
    });

    // 自动关闭
    const timer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration]);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onClose(id);
    }, 300); // 与动画时长一致
  };

  return (
    <div
      className={cn(
        'relative flex items-start gap-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm transition-all duration-300 ease-out transform',
        toastStyles[type],
        isVisible && !isLeaving
          ? 'translate-y-0 opacity-100 scale-100'
          : 'translate-y-[-20px] opacity-0 scale-95',
        isLeaving && 'translate-x-96 opacity-0'
      )}
    >
      <Icon
        className={cn('w-5 h-5 mt-0.5 flex-shrink-0', toastIconColors[type])}
      />

      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm">{title}</div>
        {description && (
          <div className="mt-1 text-sm opacity-90">{description}</div>
        )}
      </div>

      <button
        onClick={handleClose}
        className="ml-2 p-1 rounded-full hover:bg-black/10 transition-colors"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  );
}

export interface ToastData {
  type: ToastType;
  title: string;
  description?: string;
  duration?: number;
}

let toastCounter = 0;

export class ToastManager {
  private static instance: ToastManager;
  private toasts: ToastProps[] = [];
  private listeners: Array<(toasts: ToastProps[]) => void> = [];

  static getInstance() {
    if (!ToastManager.instance) {
      ToastManager.instance = new ToastManager();
    }
    return ToastManager.instance;
  }

  subscribe(listener: (toasts: ToastProps[]) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  private notify() {
    this.listeners.forEach((listener) => listener([...this.toasts]));
  }

  show(data: ToastData) {
    const id = `toast-${++toastCounter}`;
    const toast: ToastProps = {
      id,
      ...data,
      onClose: this.remove.bind(this),
    };

    this.toasts.push(toast);
    this.notify();

    return id;
  }

  remove(id: string) {
    this.toasts = this.toasts.filter((toast) => toast.id !== id);
    this.notify();
  }

  clear() {
    this.toasts = [];
    this.notify();
  }
}

export const toast = {
  success: (title: string, description?: string, duration?: number) =>
    ToastManager.getInstance().show({
      type: 'success',
      title,
      description,
      duration,
    }),

  error: (title: string, description?: string, duration?: number) =>
    ToastManager.getInstance().show({
      type: 'error',
      title,
      description,
      duration,
    }),

  warning: (title: string, description?: string, duration?: number) =>
    ToastManager.getInstance().show({
      type: 'warning',
      title,
      description,
      duration,
    }),

  info: (title: string, description?: string, duration?: number) =>
    ToastManager.getInstance().show({
      type: 'info',
      title,
      description,
      duration,
    }),
};
