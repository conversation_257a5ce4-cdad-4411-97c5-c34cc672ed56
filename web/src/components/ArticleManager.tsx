'use client';

import { useState, useEffect, useCallback } from 'react';
import { useApp } from '@/contexts/AppContext';
import { wechatApi } from '@/lib/api';
import { formatDateTime, downloadResourceViaProxy, getArticleTypeName, getArticleTypeStyle, formatDisplayTitle, generateExportFilename } from '@/lib/utils';
import { AppMsgEx } from '@/types/article';
import { Account } from '@/types/account';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/toast';
import { accountStorage } from '@/store';
import { exportSingleArticleWithHtml, exportHtmlAssetsToDisk } from '@/lib/htmlExporter';
import { convertHtmlToMarkdown, downloadMarkdownFile } from '@/lib/mdExporter';
import { convertHtmlToText, downloadTextFile } from '@/lib/txtExporter';
import { download, downloads } from '@/lib/pool';
import { userSettingsStorage } from '@/store/user-settings';

import { AccountSelector } from '@/components/AccountSelector';
import { articleStorage, articleDetailStorage } from "@/store";

export default function ArticleManager() {
  const { state, dispatch } = useApp();
  const [articles, setArticles] = useState<AppMsgEx[]>([]);
  const [totalMessageCount, setTotalMessageCount] = useState(0);
  const [loadedMessageCount, setLoadedMessageCount] = useState(0); // 已加载的消息数量
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [selectedArticles, setSelectedArticles] = useState<Set<string>>(new Set());
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadingArticles, setDownloadingArticles] = useState<Set<string>>(new Set());
  const [downloadedArticles, setDownloadedArticles] = useState<Set<string>>(new Set());
  const [exportingArticles, setExportingArticles] = useState<Set<string>>(new Set());
  const [isCheckingNewMessages, setIsCheckingNewMessages] = useState(false);
  const [openExportDropdown, setOpenExportDropdown] = useState<string | null>(null);
  const [batchExportFormat, setBatchExportFormat] = useState<'HTML' | 'TXT' | 'MD'>('HTML');
  const [selectedDirectory, setSelectedDirectory] = useState<FileSystemDirectoryHandle | null>(null);
  const [openBatchExportDropdown, setOpenBatchExportDropdown] = useState(false);
  const [isBatchExporting, setIsBatchExporting] = useState(false);
  const [proxyConfigChecked, setProxyConfigChecked] = useState(false);
  const [hasValidProxy, setHasValidProxy] = useState(false);
  const [isSyncCompleted, setIsSyncCompleted] = useState(false);
  
  // 检查代理配置的 hook
  const checkProxyConfig = useCallback(async () => {
    if (!state.user) return false;
    
    if (proxyConfigChecked) {
      return hasValidProxy;
    }
    
    try {
      const settings = await userSettingsStorage.getProxySettings(state.user.id);
      const proxies = settings.proxies || [];
      
      const isValid = proxies.length > 0;
      setHasValidProxy(isValid);
      setProxyConfigChecked(true);
      
      if (!isValid) {
        toast.error('未配置代理', '请先配置代理地址后再下载文章');
      }
      
      return isValid;
    } catch (error) {
      toast.error('检查代理配置失败', error instanceof Error ? error.message : '未知错误');
      setHasValidProxy(false);
      setProxyConfigChecked(true);
      return false;
    }
  }, [state.user, proxyConfigChecked, hasValidProxy]);
  
  // 用户变更时重置代理检查状态
  useEffect(() => {
    setProxyConfigChecked(false);
    setHasValidProxy(false);
  }, [state.user]);

  // 验证文章内容的函数
  const validateArticleContent = (htmlContent: string, title: string): boolean => {
    const displayTitle = formatDisplayTitle(title);

    if (!htmlContent.trim()) {
      toast.error('下载失败', `文章《${displayTitle}》下载的内容为空，请稍后重试`);
      return false
    }

    const parser = new DOMParser();
    const document = parser.parseFromString(htmlContent, 'text/html');
    const $jsContent = document.querySelector('#js_content');
    const $layout = document.querySelector('#js_fullscreen_layout_padding');

    if (!$jsContent) {
      if ($layout) {
        toast.error('下载失败', `文章《${displayTitle}》已被删除`);
        return false;
      }
      toast.error('下载失败', `文章《${displayTitle}》下载失败，请重试`);
      return false;
    }

    return true;
  };

  // 保存文章详情的函数
  const saveArticleDetail = async (url: string, title: string, htmlContent: string): Promise<void> => {
    if (!state.selectedAccount) {
      throw new Error('未选择账号');
    }

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const articleDetail = {
      url: url,
      title: title,
      file: blob,
      fakeid: state.selectedAccount.fakeid,
    };
    
    await articleDetailStorage.save(articleDetail);
  };

  // 检查已下载的文章
  const checkDownloadedArticles = useCallback(async () => {
    if (!state.selectedAccount) return;
    
    try {
      const downloadedList = await articleDetailStorage.getList(state.selectedAccount.fakeid);
      const downloadedUrls = new Set(downloadedList.map(detail => detail.url));
      setDownloadedArticles(downloadedUrls);
    } catch (error) {
      console.error('检查下载状态失败:', error);
    }
  }, [state.selectedAccount]);

  // 从数据库加载已存储的文章
  const loadArticlesFromDatabase = useCallback(async (userId: string, fakeid: string) => {
    // 未登录直接返回
    if (!userId || !fakeid) {
      return false
    }
    try {
      setIsLoading(true);

      // 直接获取所有文章
      const storedArticles = await articleStorage.getList(fakeid);
      
      const storedAccount = await accountStorage.getAccount(fakeid);

      if (storedArticles && storedArticles.length > 0) {
        const articles: AppMsgEx[] = storedArticles as AppMsgEx[];
        // 按发布时间倒序排序：先按 appmsgid 降序，如果相同则按 itemidx 升序
        const sortedArticles = articles.sort((a, b) => {
          // 先按 appmsgid 降序排序
          if (a.appmsgid !== b.appmsgid) {
            return b.appmsgid - a.appmsgid;
          }
          // appmsgid 相同时，按 itemidx 升序排序
          return a.itemidx - b.itemidx;
        });
        setArticles(sortedArticles);
        setTotalMessageCount(storedAccount?.total_message_count ?? articles.length);
        setLoadedMessageCount(storedAccount?.loaded_message_count ?? articles.length);
        setIsSyncCompleted(storedAccount?.sync_completed ?? false);
        dispatch({ type: 'SET_ARTICLES', payload: sortedArticles });
        return true;
      }
      
      console.log(`❌ 没有找到任何文章`);
      return false;
    } catch (error) {
      console.error('从数据库加载文章失败:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [dispatch]);

  // 保存文章到数据库 - 直接使用 API 返回的 Article 数据
  const saveArticlesToDatabase = useCallback(async (articles: AppMsgEx[], accountFakeid: string) => {
    if (!articles || articles.length === 0) return;
    
    try {
      // 直接转换为AppMsgEx格式
      const apiArticles = articles as unknown as AppMsgEx[];

      await accountStorage.saveArticles(apiArticles, accountFakeid);
    } catch (error) {
      console.error('保存文章失败:', error);
      toast.error('保存失败', error instanceof Error ? error.message : '未知错误');
    }
  }, []);

  const fetchArticles = useCallback(async (keyword = '') => {
    if (!state.selectedAccount || !state.user) return;

    try {
      setIsLoading(true);
      dispatch({ type: 'SET_ERROR', payload: null });

      const result = await wechatApi.searchArticles(
        state.selectedAccount.fakeid,
        state.user.token,
        keyword,
        '0',
        '20'
      );

      // 按发布时间倒序排序：先按 appmsgid 降序，如果相同则按 itemidx 升序
      const sortedArticles = result.articles.sort((a, b) => {
        // 先按 appmsgid 降序排序
        if (a.appmsgid !== b.appmsgid) {
          return b.appmsgid - a.appmsgid;
        }
        // appmsgid 相同时，按 itemidx 升序排序
        return a.itemidx - b.itemidx;
      });
      setArticles(sortedArticles);
      setTotalMessageCount(result.total_message_count);
      // 初始加载了20条消息
      const actualLoadedMessageCount = Math.min(20, result.total_message_count);
      setLoadedMessageCount(actualLoadedMessageCount);
      dispatch({ type: 'SET_ARTICLES', payload: sortedArticles });

      // 保存到数据库
      console.log(result)
      if (result.articles && result.articles.length > 0) {
        await saveArticlesToDatabase(result.articles, state.selectedAccount.fakeid);
        // 更新 account
        await accountStorage.updateAccountStats(state.selectedAccount.fakeid, {
          total_message_count: result.total_message_count,
          loaded_message_count: actualLoadedMessageCount,
          loaded_article_count: result.articles.length,
        })
      }
    } catch (error) {
      toast.error('获取文章失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  }, [state.selectedAccount, state.user, dispatch, saveArticlesToDatabase]);

  const  syncAllArticles = useCallback(async () => {
    if (!state.selectedAccount || !state.user || isSyncing) return;

    try {
      setIsSyncing(true);
      dispatch({ type: 'SET_ERROR', payload: null });

      let allArticles = Array.isArray(articles) ? [...articles] : [];
      let hasMore = true;
      const pageSize = 20;
      let currentMessageIndex = loadedMessageCount;

      // 从当前消息数量作为起始位置开始请求新消息
      let beginIndex = currentMessageIndex;

      while (hasMore) {
        let success = false;
        let retryCount = 0;
        const maxRetries = 3;
        let waitTime = 1000; // 初始等待时间1秒
        
        while (!success && retryCount < maxRetries) {
          try {
            const result = await wechatApi.searchArticles(
              state.selectedAccount.fakeid,
              state.user.token,
              '',
              beginIndex.toString(),
              pageSize.toString()
            );

            if (result.articles && result.articles.length > 0) {
              // 基于aid去重，过滤掉已存在的文章
              const existingAids = new Set((allArticles || []).map(article => article.aid));
              const newArticles = result.articles.filter(article =>
                !existingAids.has(article.aid) && article.aid
              );

              if (newArticles.length > 0) {
                allArticles = [...allArticles, ...newArticles];
                setArticles([...allArticles]);

                // 计算实际获取到的消息数量
                currentMessageIndex = Math.min(beginIndex + pageSize, result.total_message_count);

                // 更新数据库
                try {
                  await saveArticlesToDatabase(newArticles, state.selectedAccount.fakeid);
                  await accountStorage.updateAccountStats(state.selectedAccount.fakeid, {
                    loaded_message_count: currentMessageIndex,
                    loaded_article_count: allArticles.length
                  });
                } catch (error) {
                  console.error('更新数据库失败:', error);
                }
              }
              
              // 更新消息总数和已加载数量
              setTotalMessageCount(result.total_message_count);
              setLoadedMessageCount(currentMessageIndex);

              // 如果获取的文章数量为0或少于预期，说明可能已经是最后一页
              if (result.articles.length === 0) {
                hasMore = false;
              } else {
                beginIndex += pageSize;
                // 如果已经加载的消息数达到总数，停止加载
                if (currentMessageIndex >= result.total_message_count) {
                  hasMore = false;
                }
              }

              // 如果没有新文章了，也停止请求
              if (newArticles.length === 0 && result.articles.length > 0) {
                hasMore = false;
              }

              success = true; // 请求成功
            } else {
              hasMore = false;
              success = true; // 没有文章也算成功，结束循环
            }
          } catch (error) {
            retryCount++;
            console.error(`拉取文章失败，第 ${retryCount} 次重试:`, error);
            
            if (retryCount < maxRetries) {
              toast.info('拉取遇到问题', `正在进行第 ${retryCount} 次重试，等待 ${waitTime / 1000} 秒...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
              waitTime *= 2; // 指数退避，下次等待时间翻倍
            } else {
              // 达到最大重试次数，抛出错误
              throw new Error(`多次重试失败: ${error instanceof Error ? error.message : '未知错误'}`);
            }
          }
        }

        // 成功请求后添加延迟避免过于频繁
        if (success && hasMore) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 最终更新全局状态 - 按发布时间倒序排序：先按 appmsgid 降序，如果相同则按 itemidx 升序
      const finalSortedArticles = allArticles.sort((a, b) => {
        // 先按 appmsgid 降序排序
        if (a.appmsgid !== b.appmsgid) {
          return b.appmsgid - a.appmsgid;
        }
        // appmsgid 相同时，按 itemidx 升序排序
        return a.itemidx - b.itemidx;
      });
      dispatch({ type: 'SET_ARTICLES', payload: finalSortedArticles });
      setIsSyncCompleted(true); // 标记拉取完成 - 只有成功完成整个拉取流程才会到这里
      // 更新数据库中的拉取完成状态
      if (state.selectedAccount) {
        await accountStorage.updateAccountStats(state.selectedAccount.fakeid, {
          sync_completed: true
        });
      }
      toast.success('同步完成', `总共加载了 ${finalSortedArticles.length} 篇文章`);
    } catch (error) {
      toast.error('同步文章失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsSyncing(false);
    }
  }, [state.selectedAccount, state.user, dispatch, articles, isSyncing, loadedMessageCount, saveArticlesToDatabase]);

  // 检测并拉取新消息
  const checkNewMessages = useCallback(async () => {
    if (!state.selectedAccount || !state.user || isCheckingNewMessages) return;

    try {
      setIsCheckingNewMessages(true);

      // 调用搜索接口，只获取1条消息来读取total_count
      const result = await wechatApi.searchArticles(state.selectedAccount.fakeid, state.user.token, '', '0', '1');
      
      const serverTotalCount = result.total_message_count;
      const localTotalCount = totalMessageCount;

      console.log(`📊 消息数量对比: 服务器 ${serverTotalCount} vs 本地 ${localTotalCount}`);

      if (serverTotalCount > localTotalCount) {
        // 有新消息，计算需要拉取的数量
        const newMessageCount = serverTotalCount - localTotalCount;
        console.log(`🆕 发现 ${newMessageCount} 条新消息，开始拉取...`);
        
        toast.success('发现新消息', `有 ${newMessageCount} 条新消息，正在拉取...`);
        
        // 拉取新消息 - 从最新开始拉取 newMessageCount 条
        const pageSize = Math.min(20, newMessageCount); // 每次最多拉取20条
        let pulledMessages: AppMsgEx[] = [];
        let remainingCount = newMessageCount;
        let beginIndex = 0;

        while (remainingCount > 0 && beginIndex < newMessageCount) {
          const currentPageSize = Math.min(pageSize, remainingCount);
          
          try {
            const newResult = await wechatApi.searchArticles(
              state.selectedAccount.fakeid, 
              state.user.token, 
              '', 
              beginIndex.toString(), 
              currentPageSize.toString()
            );

            if (newResult.articles && newResult.articles.length > 0) {
              // 过滤掉已存在的文章（基于aid去重）
              const existingAids = new Set(articles.map(article => article.aid));
              const newArticles = newResult.articles.filter(article =>
                !existingAids.has(article.aid) && article.aid
              );

              pulledMessages = [...pulledMessages, ...newArticles];
              remainingCount -= currentPageSize;
              beginIndex += currentPageSize;
              
              console.log(`📥 已拉取 ${beginIndex}/${newMessageCount} 条消息`);
              
              // 添加延迟避免过于频繁的请求
              if (remainingCount > 0) {
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            } else {
              break; // 没有更多消息了
            }
          } catch (error) {
            console.error(`拉取第 ${beginIndex}-${beginIndex + currentPageSize} 条消息失败:`, error);
            break;
          }
        }

        if (pulledMessages.length > 0) {
          // 按发布时间倒序排序新消息：先按 appmsgid 降序，如果相同则按 itemidx 升序
          const sortedNewMessages = pulledMessages.sort((a, b) => {
            // 先按 appmsgid 降序排序
            if (a.appmsgid !== b.appmsgid) {
              return b.appmsgid - a.appmsgid;
            }
            // appmsgid 相同时，按 itemidx 升序排序
            return a.itemidx - b.itemidx;
          });
          
          // 将新消息插入到现有文章列表的开头（保持整体时间倒序）
          const updatedArticles = [...sortedNewMessages, ...articles];
          
          setArticles(updatedArticles);
          dispatch({ type: 'SET_ARTICLES', payload: updatedArticles });

          // 保存新消息到数据库
          await saveArticlesToDatabase(sortedNewMessages, state.selectedAccount.fakeid);
          
          // 更新计数器（以消息数量为准，而非文章数量）
          const pulledMessageCount = beginIndex; // 实际处理的消息数量
          setTotalMessageCount(serverTotalCount);
          setLoadedMessageCount(loadedMessageCount + pulledMessageCount);
          
          // 更新数据库中的账号统计信息
          await accountStorage.updateAccountStats(state.selectedAccount.fakeid, {
            total_message_count: serverTotalCount,
            loaded_message_count: loadedMessageCount + pulledMessageCount,
            loaded_article_count: updatedArticles.length
          });

          toast.success('拉取完成', `成功拉取了 ${pulledMessageCount} 条新消息`);
        } else {
          // 更新总数但没有新的实际文章
          setTotalMessageCount(serverTotalCount);
          await accountStorage.updateMessageTotalCount(state.selectedAccount.fakeid, serverTotalCount);
          toast.info('已更新', '消息总数已更新，但没有发现新的文章');
        }
      } else {
        toast.info('暂无新消息', '公众号消息已是最新');
      }
    } catch (error) {
      console.error('检测新消息失败:', error);
      toast.error('检测失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsCheckingNewMessages(false);
    }
  }, [state.selectedAccount, state.user, totalMessageCount, articles, loadedMessageCount, isCheckingNewMessages, dispatch, saveArticlesToDatabase]);

  // 设置客户端状态
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 点击外部关闭导出下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openExportDropdown || openBatchExportDropdown) {
        const target = event.target as Element;
        // 检查点击的元素是否在导出下拉容器内
        const exportDropdownContainer = target.closest('[data-export-dropdown]');
        const batchExportDropdownContainer = target.closest('[data-batch-export-dropdown]');
        if (!exportDropdownContainer && !batchExportDropdownContainer) {
          setOpenExportDropdown(null);
          setOpenBatchExportDropdown(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openExportDropdown, openBatchExportDropdown]);

  useEffect(() => {
    const initializeArticles = async () => {
      // 只有在用户已登录且选择了账号时才初始化文章
      if (state.user && state.selectedAccount) {
        setSelectedArticles(new Set());
        setTotalMessageCount(0);
        setLoadedMessageCount(0);
        setIsSyncCompleted(false);
        
        // 先从数据库加载已存储的文章
        const hasStoredArticles = await loadArticlesFromDatabase(state.user.id, state.selectedAccount.fakeid);
        
        // 如果数据库中没有文章，则从 API 获取
        if (!hasStoredArticles) {
          fetchArticles('');
        }
        
        // 检查已下载的文章状态
        await checkDownloadedArticles();
      } else {
        setArticles([]);
        setTotalMessageCount(0);
        setLoadedMessageCount(0);
        setSelectedArticles(new Set());
        setDownloadedArticles(new Set());
        setIsSyncCompleted(false);
      }
    };

    initializeArticles();
  }, [state.user, state.selectedAccount, fetchArticles, loadArticlesFromDatabase, checkDownloadedArticles]);

  const handleAccountSelect = (account: Account | null) => {
    if (account) {
      // 将账号信息设置为当前账号
      dispatch({ type: 'SET_SELECTED_ACCOUNT', payload: account });
    } else {
      dispatch({ type: 'SET_SELECTED_ACCOUNT', payload: null });
    }
  };

  const toggleArticleSelection = (link: string, isDeleted: boolean) => {
    // 如果文章已删除，不允许选择
    if (isDeleted) {
      return;
    }
    
    const newSelected = new Set(selectedArticles);
    if (newSelected.has(link)) {
      newSelected.delete(link);
    } else {
      newSelected.add(link);
    }
    setSelectedArticles(newSelected);
  };

  const toggleSelectAll = () => {
    // 只处理未删除的文章
    const nonDeletedArticles = articles.filter(article => !article.is_deleted);
    
    if (selectedArticles.size === nonDeletedArticles.length && nonDeletedArticles.length > 0) {
      setSelectedArticles(new Set());
    } else {
      setSelectedArticles(new Set(nonDeletedArticles.map(article => article.link)));
    }
  };

  const selectDirectory = async () => {
    try {
      // 检查浏览器是否支持 File System Access API
      if ('showDirectoryPicker' in window) {
        const dirHandle = await (window as Window & {
          showDirectoryPicker: () => Promise<FileSystemDirectoryHandle>;
        }).showDirectoryPicker();
        setSelectedDirectory(dirHandle);
        toast.success('目录选择成功', `已选择目录: ${dirHandle.name}`);
      } else {
        toast.error('不支持目录选择', '您的浏览器不支持文件系统访问API');
      }
    } catch (error) {
      if ((error as Error).name !== 'AbortError') {
        toast.error('目录选择失败', error instanceof Error ? error.message : '未知错误');
      }
    }
  };

  const downloadArticle = async (url: string, title: string) => {
    if (!state.selectedAccount || !state.user) return;
    
    // 检查代理配置
    if (!(await checkProxyConfig())) {
      return;
    }
    
    try {
      setDownloadingArticles(prev => new Set([...prev, url]));

      const articleDownloadFn = async (articleUrl: string, proxyUrl: string): Promise<number> => {
        // 下载HTML内容
        const response = await downloadResourceViaProxy(articleUrl, proxyUrl);
        const fullHTML = await response.text();

        // 验证下载内容
        const isValid = validateArticleContent(fullHTML, title);
        if (!isValid) {
          return 0; // 验证失败时返回0字节
        }

        // 保存文章详情到数据库
        await saveArticleDetail(articleUrl, title, fullHTML);

        return new Blob([fullHTML]).size;
      };

      const result = await download(url, articleDownloadFn);
      
      if (!result.success) {
        throw new Error(`下载失败: ${result.error || '网络或代理问题'}`);
      }

      // 更新已下载状态
      setDownloadedArticles(prev => new Set([...prev, url]));
      
      toast.success('下载成功', `文章《${formatDisplayTitle(title)}》下载完成`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error(`下载文章失败 [${title}]:`, error);
      
      // 根据错误类型提供更有针对性的提示
      if (errorMessage.includes('已被删除')) {
        toast.error('文章已删除', `文章《${formatDisplayTitle(title)}》已被删除`);
      } else if (errorMessage.includes('代理')) {
        toast.error('代理问题', `文章《${formatDisplayTitle(title)}》下载失败：${errorMessage}`);
      } else if (errorMessage.includes('网络')) {
        toast.error('网络问题', `文章《${formatDisplayTitle(title)}》下载失败：${errorMessage}，请检查网络连接`);
      } else {
        toast.error('下载失败', `文章《${formatDisplayTitle(title)}》下载失败：${errorMessage}`);
      }
    } finally {
      setDownloadingArticles(prev => {
        const newSet = new Set(prev);
        newSet.delete(url);
        return newSet;
      });
    }
  };

  const batchDownload = async () => {
    if (selectedArticles.size === 0) return;
    if (!state.user) return;
    
    // 检查代理配置
    if (!(await checkProxyConfig())) {
      return;
    }

    try {
      setIsDownloading(true);
      
      const selectedArticlesList = articles.filter(article => 
        selectedArticles.has(article.link) && 
        !article.is_deleted && // 过滤已删除的文章
        !downloadedArticles.has(article.link) // 过滤已下载的文章
      );
      
      if (selectedArticlesList.length === 0) {
        toast.info('无需下载', '所选文章均已下载');
        return;
      }
      
      // 将文章列表转换为 URL 字符串数组
      const articleUrls = selectedArticlesList.map(article => article.link);
      
      // 批量下载的核心逻辑
      const articleDownloadFn = async (articleUrl: string, proxyUrl: string): Promise<number> => {
        const article = selectedArticlesList.find(a => a.link === articleUrl);
        if (!article) throw new Error(`找不到文章: ${articleUrl}`);
        
        // 下载HTML内容
        const response = await downloadResourceViaProxy(articleUrl, proxyUrl);
        const fullHTML = await response.text();

        // 验证下载内容
        const isValid = validateArticleContent(fullHTML, article.title);
        if (!isValid) {
          return 0; // 验证失败时返回0字节
        }

        // 保存文章详情到数据库
        await saveArticleDetail(articleUrl, article.title, fullHTML);
        
        return new Blob([fullHTML]).size;
      };
      
      // 使用 downloads 函数进行批量下载
      const results = await downloads(articleUrls, articleDownloadFn, true);

      // 统计成功和失败数量
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      // 更新已下载状态（成功的文章）
      const successfulUrls = results.filter(r => r.success).map(r => r.url);
      setDownloadedArticles(prev => {
        const newSet = new Set(prev);
        successfulUrls.forEach(url => newSet.add(url));
        return newSet;
      });

      // 找出失败的文章，保持选中状态以便重试
      const failedArticleLinks = new Set<string>();
      results.forEach((result) => {
        if (!result.success) {
          failedArticleLinks.add(result.url);
        }
      });

      // 只保留失败的文章处于选中状态
      setSelectedArticles(failedArticleLinks);
      
      if (successCount > 0) {
        const message = `成功下载 ${successCount} 篇文章` + 
          (failureCount > 0 ? `，失败 ${failureCount} 篇` : '');
        const detail = failureCount > 0 ? '失败的文章已重新选中，可再次下载重试' : '';
        toast.success('批量下载完成', `${message}${detail ? '，' + detail : ''}`);
      } else {
        toast.error('批量下载失败', '所有文章下载均失败，请检查网络连接或代理设置');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('批量下载失败:', error);
      
      // 根据错误类型提供更有针对性的提示
      if (errorMessage.includes('代理')) {
        toast.error('代理问题', errorMessage);
      } else if (errorMessage.includes('网络')) {
        toast.error('网络问题', `${errorMessage}，请检查网络连接`);
      } else {
        toast.error('批量下载失败', errorMessage);
      }
    } finally {
      setIsDownloading(false);
    }
  };

  // 单个文章导出处理函数
  const handleSingleExport = async (article: AppMsgEx, format: 'HTML' | 'TXT' | 'MD') => {
    try {
      // 设置导出中状态
      setExportingArticles(prev => new Set([...prev, article.link]));
      
      // 从数据库获取文章内容
      const articleDetail = await articleDetailStorage.get(article.link);
      if (!articleDetail) {
        toast.error('导出失败', '未找到文章内容，请先下载文章');
        return;
      }

      // 将Blob转换为HTML字符串
      const htmlContent = await articleDetail.file.text();

      if (format === 'HTML') {
        await exportSingleArticleWithHtml(article, htmlContent);
        toast.success('导出成功', `文章《${formatDisplayTitle(article.title)}》已导出为HTML格式`);
      } else if (format === 'MD') {
        // 转换为Markdown并下载
        const markdownContent = convertHtmlToMarkdown(htmlContent, article);
        downloadMarkdownFile(
          markdownContent, 
          article.title, 
          article.update_time, 
          state.selectedAccount?.nickname,
          article.itemidx
        );
        toast.success('导出成功', `文章《${formatDisplayTitle(article.title)}》已导出为Markdown格式`);
      } else if (format === 'TXT') {
        // 转换为纯文本并下载
        const textContent = convertHtmlToText(htmlContent);
        downloadTextFile(
          textContent, 
          article.title, 
          article.update_time, 
          state.selectedAccount?.nickname,
          article.itemidx
        );
        toast.success('导出成功', `文章《${formatDisplayTitle(article.title)}》已导出为纯文本格式`);
      }
    } catch (error) {
      console.error('导出失败:', error);
      toast.error('导出失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      // 移除导出中状态
      setExportingArticles(prev => {
        const newSet = new Set(prev);
        newSet.delete(article.link);
        return newSet;
      });
    }
  };

  const batchExport = async () => {
    if (selectedArticles.size === 0) {
      toast.info('无选中文章', '请先选择要导出的文章');
      return;
    }

    try {
      setIsBatchExporting(true);

      // 检查目录选择
      if (!selectedDirectory) {
        toast.error('请选择目录', '请先选择文件保存目录');
        return;
      }

      // 获取选中的文章
      const selectedArticlesList = articles.filter(article => 
        selectedArticles.has(article.link)
      );

      // 检查选中文章中的下载状态
      const downloadedCount = selectedArticlesList.filter(article => 
        downloadedArticles.has(article.link)
      ).length;

      const undownloadedCount = selectedArticlesList.length - downloadedCount;

      if (undownloadedCount > 0) {
        toast.error(
          '部分文章未下载', 
          `选中的 ${selectedArticlesList.length} 篇文章中有 ${undownloadedCount} 篇未下载，请先下载所有选中的文章！`
        );
        return;
      }

      if (downloadedCount === 0) {
        toast.info('无可导出文章', '所选文章均未下载，请先下载文章再导出');
        return;
      }

      let successCount = 0;
      const failedArticleLinks = new Set<string>();

      // MD格式处理：转换并保存到目录
      if (batchExportFormat === 'MD') {
        for (const article of selectedArticlesList) {
          if (downloadedArticles.has(article.link)) {
            try {
              const articleDetail = await articleDetailStorage.get(article.link);
              if (articleDetail) {
                const htmlContent = await articleDetail.file.text();
                if (validateArticleContent(htmlContent, article.title)) {
                  // 转换为Markdown
                  const markdownContent = convertHtmlToMarkdown(htmlContent, article);
                  
                  // 生成安全的文件名
                  const accountName = state.selectedAccount?.nickname || '公众号';
                  const fileName = `${generateExportFilename(article.title, article.update_time, accountName, article.itemidx)}.md`;
                  
                  // 创建并保存文件到选中目录
                  const fileHandle = await selectedDirectory.getFileHandle(fileName, { create: true });
                  const writable = await fileHandle.createWritable();
                  await writable.write(markdownContent);
                  await writable.close();
                  
                  successCount++;
                }
              }
            } catch (error) {
              console.error(`导出文章失败: ${article.title}`, error);
              failedArticleLinks.add(article.link);
            }
          }
        }
      } else if (batchExportFormat === 'TXT') {
        // TXT格式处理：转换并保存到目录
        for (const article of selectedArticlesList) {
          if (downloadedArticles.has(article.link)) {
            try {
              const articleDetail = await articleDetailStorage.get(article.link);
              if (articleDetail) {
                const htmlContent = await articleDetail.file.text();
                if (validateArticleContent(htmlContent, article.title)) {
                  // 转换为纯文本
                  const textContent = convertHtmlToText(htmlContent);
                  
                  // 生成安全的文件名
                  const accountName = state.selectedAccount?.nickname || '公众号';
                  const fileName = `${generateExportFilename(article.title, article.update_time, accountName, article.itemidx)}.txt`;
                  
                  // 创建并保存文件到选中目录
                  const fileHandle = await selectedDirectory.getFileHandle(fileName, { create: true });
                  const writable = await fileHandle.createWritable();
                  await writable.write(textContent);
                  await writable.close();
                  
                  successCount++;
                }
              }
            } catch (error) {
              console.error(`导出文章失败: ${article.title}`, error);
              failedArticleLinks.add(article.link);
            }
          }
        }
      } else if (batchExportFormat === 'HTML') {
        // HTML格式处理
        const BATCH_EXPORT_SIZE = 5; // 批量导出时每批处理的文章数量
        for (let i = 0; i < selectedArticlesList.length; i += BATCH_EXPORT_SIZE) {
          const batch = selectedArticlesList.slice(i, i + BATCH_EXPORT_SIZE);
          const batchResults = await Promise.allSettled(
            batch.map(async (article) => {
              if (downloadedArticles.has(article.link)) {
                const articleDetail = await articleDetailStorage.get(article.link);
                if (articleDetail) {
                  const htmlContent = await articleDetail.file.text();

                  // 生成文章目录名
                  const accountName = state.selectedAccount?.nickname || '公众号';
                  const folderName = generateExportFilename(article.title, article.update_time, accountName, article.itemidx);

                  // 创建文章目录（此时selectedDirectory已经检查过不为null）
                  if (selectedDirectory) {
                    try {
                      const articleDir = await selectedDirectory.getDirectoryHandle(folderName, { create: true });

                      // 直接导出HTML和资源到目录
                      await exportHtmlAssetsToDisk(htmlContent, article.title, articleDir);
                    } catch (error) {
                      console.error(`创建目录 "${folderName}" 失败:`, error);
                      // 如果是文件名问题，尝试使用简化的文件名
                      if (error instanceof TypeError && error.message.includes('Name is not allowed')) {
                        const fallbackName = `article_${article.itemidx || Date.now()}`;
                        console.log(`尝试使用备用文件名: ${fallbackName}`);
                        try {
                          const articleDir = await selectedDirectory.getDirectoryHandle(fallbackName, { create: true });
                          await exportHtmlAssetsToDisk(htmlContent, article.title, articleDir);
                        } catch (fallbackError) {
                          console.error(`使用备用文件名也失败:`, fallbackError);
                          throw fallbackError;
                        }
                      } else {
                        throw error;
                      }
                    }
                  }
                  
                  return true;
                }
              }
              return false;
            })
          );
          
          // 统计成功数量和记录失败的文章
          batchResults.forEach((result, index) => {
            const article = batch[index];
            if (result.status === 'fulfilled' && result.value === true) {
              successCount++;
            } else {
              failedArticleLinks.add(article.link);
              if (result.status === 'rejected') {
                const errorMsg = result.reason?.message || String(result.reason);
                console.error(`导出文章 "${article.title}" 失败:`, result.reason);

                // 特别处理文件名错误
                if (errorMsg.includes('Name is not allowed')) {
                  console.error(`文章 "${article.title}" 的文件名包含不被 File System API 允许的字符`);
                }
              }
            }
          });
        }
      }
      
      // 只保留失败的文章处于选中状态
      setSelectedArticles(failedArticleLinks);
      
      if (successCount > 0) {
        const dirName = selectedDirectory?.name || '所选目录';
        toast.success('批量导出完成', `成功导出了 ${successCount} 篇文章到 ${dirName} 目录 ${failedArticleLinks.size > 0 ? `，失败 ${failedArticleLinks.size} 篇，再次点击批量导出即可重试` : ''}`);
      } else {
        toast.error('导出失败', '没有文章成功导出');
      }
      
    } catch (error) {
      console.error('批量导出失败:', error);
      toast.error('批量导出失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsBatchExporting(false);
    }
  };

  const copyToClipboard = async (text: string, title: string) => {
    const displayTitle = formatDisplayTitle(title);
      
    try {
      await navigator.clipboard.writeText(text);
      toast.success('复制成功', `已复制文章《${displayTitle}》的链接`);
    } catch {
      // 降级方案：使用传统的复制方法
      try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-9999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        toast.success('复制成功', `已复制文章《${displayTitle}》的链接`);
      } catch {
        toast.error('复制失败', '无法复制到剪贴板');
      }
    }
  };



  if (!state.user) {
    return (
      <Card className="bg-white shadow-sm border-0">
        <CardContent className="flex flex-col items-center justify-center py-20">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            请先登录
          </h3>
          <p className="text-sm text-gray-500 text-center max-w-xs">
            登录后即可开始使用
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm border-0">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-bold text-gray-900 pb-2">文章批量导出</CardTitle>

        {/* 公众号选择器 */}
        <div className="space-y-4">
          <div className="flex space-x-2">
            <div className="flex-1">
              <AccountSelector onAccountSelect={handleAccountSelect} />
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={syncAllArticles}
                disabled={!state.selectedAccount || isSyncing || isSyncCompleted}
                variant="outline"
                className="h-[42px] px-4 border-purple-200 bg-purple-50 hover:bg-purple-100 hover:border-purple-300 hover:text-purple-700 text-purple-700 whitespace-nowrap transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 1024 1024">
                    <path d="M624 706.3h-74.1V464c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v242.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.7c3.2 4.1 9.4 4.1 12.6 0l112-141.7c4.1-5.2 0.4-12.9-6.3-12.9z" />
                    <path d="M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10C846.1 454.5 884 503.8 884 560c0 33.1-12.9 64.3-36.3 87.7-23.4 23.4-54.5 36.3-87.6 36.3H720c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C870.5 760 960 670.5 960 560c0-92.7-63.1-170.7-148.6-193.3z" />
                  </svg>
                  <span>
                    {isSyncCompleted 
                      ? `拉取完成（${loadedMessageCount}/${totalMessageCount}）`
                      : isSyncing 
                        ? `拉取中...（${loadedMessageCount}/${totalMessageCount}）` 
                        : `拉取所有消息（${loadedMessageCount}/${totalMessageCount}）`
                    }
                  </span>
                </div>
              </Button>
              
              <Button
                onClick={checkNewMessages}
                disabled={!state.selectedAccount || isCheckingNewMessages || !isSyncCompleted}
                variant="outline"
                className="h-[42px] px-4 border-yellow-200 bg-yellow-50 hover:bg-yellow-100 hover:border-yellow-300 hover:text-yellow-700 text-yellow-700 whitespace-nowrap transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="flex items-center space-x-2">
                  <svg className={`w-4 h-4 ${isCheckingNewMessages ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>{isCheckingNewMessages ? '同步中...' : '同步最新消息'}</span>
                </div>
              </Button>
            </div>
          </div>


        </div>
      </CardHeader>

      <CardContent>
        {!state.selectedAccount ? (
          <div className="text-center py-12">
            <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
            <p className="text-gray-500">请先选择要导出的公众号</p>
          </div>
        ) : isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-500">加载文章中...</p>
          </div>
        ) : articles.length === 0 ? (
          <div className="text-center py-12">
            <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-gray-500">
              该公众号暂无文章
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* 批量操作栏 */}
            {articles.length > 0 && (
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg min-h-[60px]">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">
                    已选择 
                    <span className="mx-1 px-2 py-0.5 bg-gray-100 text-yellow-500 text-base font-bold rounded">
                      {selectedArticles.size} / {articles.filter(article => !article.is_deleted).length}
                    </span>
                    篇文章
                    <span className="text-xs text-gray-400">
                      （共 {articles.length} 篇，其中 {articles.filter(article => article.is_deleted).length} 篇已删除）
                    </span>
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    onClick={batchDownload}
                    disabled={isDownloading || selectedArticles.size === 0}
                    className={`bg-blue-50 border border-blue-200 hover:bg-blue-100 hover:border-blue-300 text-blue-700 transition-all cursor-pointer disabled:cursor-not-allowed flex items-center ${selectedArticles.size === 0 ? 'opacity-50' : 'opacity-100'
                      }`}
                  >
                    {isDownloading ? (
                      <>
                        <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        下载中
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v12m-4-4l4 4 4-4M4 17v2a1 1 0 001 1h14a1 1 0 001-1v-2" />
                        </svg>
                        批量下载文章
                      </>
                    )}
                  </Button>
                  
                  {/* 批量导出下拉菜单 */}
                  <div className="relative" data-batch-export-dropdown>
                    <Button
                      onClick={() => setOpenBatchExportDropdown(!openBatchExportDropdown)}
                      disabled={selectedArticles.size === 0 || isBatchExporting}
                      className={`bg-green-50 border border-green-200 hover:bg-green-100 hover:border-green-300 text-green-700 transition-all cursor-pointer disabled:cursor-not-allowed flex items-center ${selectedArticles.size === 0 || isBatchExporting ? 'opacity-50' : 'opacity-100'
                        }`}
                    >
                      {isBatchExporting ? (
                        <>
                          <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          导出中
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                          批量导出文章
                          <svg 
                            className={`w-3 h-3 transform transition-transform duration-200 ${
                              openBatchExportDropdown ? 'rotate-180' : 'rotate-0'
                            }`} 
                            fill="currentColor" 
                            viewBox="0 0 20 20"
                          >
                            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </>
                      )}
                    </Button>
                    
                    {openBatchExportDropdown && selectedArticles.size > 0 && (
                      <div className="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-xl z-50">
                        <div className="p-4">
                          <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">导出格式</label>
                            <div className="grid grid-cols-3 gap-2">
                              {[
                                { value: 'HTML', label: 'HTML', available: true },
                                { value: 'TXT', label: 'TXT', available: true },
                                { value: 'MD', label: 'MD', available: true }
                              ].map((format) => (
                                <button
                                  key={format.value}
                                  onClick={() => format.available && setBatchExportFormat(format.value as 'HTML' | 'TXT' | 'MD')}
                                  className={`text-xs px-3 py-2 rounded-md border transition-all cursor-pointer ${
                                    batchExportFormat === format.value
                                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                                      : format.available
                                        ? 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                                        : 'bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed'
                                  }`}
                                  disabled={!format.available}
                                >
                                  {format.label}
                                </button>
                              ))}
                            </div>
                          </div>
                          
                          <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">保存位置</label>
                            <div className="space-y-2">
                              {selectedDirectory ? (
                                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 flex items-center justify-between">
                                  <div className="flex items-center space-x-2 min-w-0 flex-1">
                                    <svg className="w-5 h-5 text-emerald-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                      <path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z" />
                                    </svg>
                                    <span className="text-sm font-medium text-gray-700 truncate">{selectedDirectory.name}</span>
                                  </div>
                                  <button
                                    onClick={() => setSelectedDirectory(null)}
                                    className="text-gray-400 hover:text-red-500 transition-colors flex-shrink-0 ml-2 cursor-pointer"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                  </button>
                                </div>
                              ) : (
                                <button
                                  onClick={selectDirectory}
                                  className="w-full text-sm bg-gray-50 border border-dashed border-gray-300 rounded-lg px-3 py-3 hover:bg-gray-100 hover:border-gray-400 text-gray-500 transition-colors flex items-center justify-center space-x-2 cursor-pointer"
                                >
                                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                  </svg>
                                  <span>选择保存目录</span>
                                </button>
                              )}
                            </div>
                          </div>
                          
                          <div className="pt-3 border-t border-gray-100">
                            <div className="flex justify-between items-center">
                              <div className="text-xs text-gray-500">
                                将导出 {selectedArticles.size} 篇文章
                              </div>
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => setOpenBatchExportDropdown(false)}
                                  className="text-xs px-3 py-1.5 text-gray-600 hover:text-gray-800 transition-colors cursor-pointer"
                                >
                                  取消
                                </button>
                                <button
                                  onClick={() => {
                                    batchExport();
                                    setOpenBatchExportDropdown(false);
                                  }}
                                  disabled={!selectedDirectory}
                                  className="text-xs px-4 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all cursor-pointer"
                                >
                                  开始导出
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 文章表格 - 带固定表头 */}
            <div className="overflow-x-auto border border-gray-200 rounded-lg">
              <div className="relative" style={{ height: 'calc(100vh - 320px)', minHeight: '400px' }}>
                <table className="w-full bg-white" style={{ minWidth: '2200px', tableLayout: 'fixed' }}>
                  <thead className="bg-gray-50 sticky top-0 z-40 shadow-sm">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-50" style={{ width: '50px' }}>
                        <div className="flex items-center justify-center">
                          <input
                            type="checkbox"
                            checked={(() => {
                              const nonDeletedArticles = articles.filter(article => !article.is_deleted);
                              return selectedArticles.size === nonDeletedArticles.length && nonDeletedArticles.length > 0;
                            })()}
                            ref={(input) => {
                              if (input) {
                                const nonDeletedArticles = articles.filter(article => !article.is_deleted);
                                input.indeterminate = selectedArticles.size > 0 && selectedArticles.size < nonDeletedArticles.length;
                              }
                            }}
                            onChange={toggleSelectAll}
                            className="custom-checkbox"
                          />
                        </div>
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '60px' }}>序号</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '100px' }}>ID</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '80px' }}>封面</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '320px' }}>标题</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '220px' }}>摘要</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '100px' }}>作者</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '100px' }}>文章类型</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '90px' }}>是否原创</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '90px' }}>媒体时长</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '120px' }}>所属合集</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '140px' }}>创建时间</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '140px' }}>发布时间</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '80px' }}>状态</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '90px' }}>下载</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '90px' }}>导出</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '60px' }}>更多</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                  {articles.map((article, index) => (
                    <tr
                      key={article.aid}
                      className={`transition-colors ${
                        selectedArticles.has(article.link) 
                          ? 'table-row-selected' 
                          : 'hover:bg-gray-50'
                      } ${
                        index > 0 ? 'border-t border-gray-200' : ''
                      }`}
                    >
                      {/* 复选框 */}
                      <td className="px-4 py-4 whitespace-nowrap sticky left-0 bg-white z-30" style={{ width: '50px' }}>
                        <div className="flex items-center justify-center">
                          <input
                            type="checkbox"
                            checked={selectedArticles.has(article.link)}
                            disabled={article.is_deleted}
                            onChange={() => toggleArticleSelection(article.link, article.is_deleted)}
                            className={`custom-checkbox ${article.is_deleted ? 'opacity-50 cursor-not-allowed' : ''}`}
                          />
                        </div>
                      </td>
                      
                      {/* 序号 */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500" style={{ width: '60px' }}>
                        {index + 1}
                      </td>
                      
                      {/* ID */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900" style={{ width: '100px' }}>
                        {article.aid}
                      </td>
                      
                      {/* 封面 */}
                      <td className="px-4 py-4 whitespace-nowrap" style={{ width: '80px' }}>
                        {article.content ? (
                          // 文本显示TXT图标 - 灰色风格
                          <div className="w-12 h-12 bg-gray-50 rounded-md flex items-center justify-center border border-gray-200">
                            <span className="text-gray-500 text-xs font-medium">TXT</span>
                          </div>
                        ) : article.cover && isClient ? (
                          // 图文显示封面图片
                          <img
                            src={article.cover}
                            alt={article.title}
                            className="w-12 h-12 object-cover rounded-md"
                            referrerPolicy="no-referrer"
                          />
                        ) : (
                          // 图文无封面时的占位符
                          <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                        )}
                      </td>
                      
                      {/* 标题 - 可点击跳转 */}
                      <td className="px-4 py-4" style={{ width: '320px' }}>
                        <div className="flex items-center">
                          <a
                            href={article.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={`font-medium text-sm leading-tight transition-colors cursor-pointer block overflow-hidden text-ellipsis whitespace-nowrap ${
                              article.is_deleted 
                                ? 'text-gray-400 line-through' 
                                : 'text-gray-900 hover:text-blue-600'
                            }`}
                            style={{ maxWidth: '300px' }}
                            title={`${article.title}`}
                          >
                            {article.title}
                          </a>
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              copyToClipboard(article.link, article.title);
                            }}
                            className="ml-1 p-0.5 text-gray-400 hover:text-blue-600 transition-colors cursor-pointer flex-shrink-0"
                            title="复制链接"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                            </svg>
                          </button>
                        </div>
                      </td>
                      
                      {/* 摘要 */}
                      <td className="px-4 py-4 max-w-0" style={{ width: '220px' }}>
                        <div className="text-xs text-gray-500 truncate" title={article.digest}>
                          {article.digest || '-'}
                        </div>
                      </td>
                      
                      {/* 作者 */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500" style={{ width: '100px' }}>
                        {article.author_name || '-'}
                      </td>
                      
                      {/* 文章类型 */}
                      <td className="px-4 py-4 whitespace-nowrap" style={{ width: '100px' }}>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded border ${getArticleTypeStyle(article.item_show_type)}`}>
                          {getArticleTypeName(article.item_show_type)}
                        </span>
                      </td>
                      
                      {/* 是否原创 */}
                      <td className="px-4 py-4 whitespace-nowrap" style={{ width: '90px' }}>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded border ${
                          article.copyright_type === 1 && article.copyright_stat === 1 ? 'bg-purple-50 text-purple-700 border-purple-200' : 'bg-gray-50 text-gray-600 border-gray-200'
                        }`}>
                          {article.copyright_type === 1 && article.copyright_stat === 1 ? '原创' : '非原创'}
                        </span>
                      </td>

                      {/* 媒体时长 */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500" style={{ width: '90px' }}>
                        {article.media_duration || '-'}
                      </td>

                      {/* 所属合集 */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500" style={{ width: '120px' }}>
                        <div className="overflow-hidden" title={article.appmsg_album_titles?.join(", ")}>
                          {(article.appmsg_album_titles?.length ?? 0) > 0 ?
                              <div className="truncate text-xs text-blue-600">
                                {article.appmsg_album_titles?.join(", ")}
                              </div>
                              : '-'
                          }
                        </div>
                      </td>
                      
                      {/* 创建时间 */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500" style={{ width: '140px' }}>
                        {formatDateTime(article.create_time)}
                      </td>
                      
                      {/* 发布时间 */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500" style={{ width: '140px' }}>
                        {formatDateTime(article.update_time)}
                      </td>
                      
                      {/* 状态 */}
                      <td className="px-4 py-4 whitespace-nowrap" style={{ width: '80px' }}>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded border ${
                          article.is_deleted ? 'bg-red-50 text-red-700 border-red-200' : 'bg-green-50 text-green-700 border-green-200'
                        }`}>
                          {article.is_deleted ? '已删除' : '正常'}
                        </span>
                      </td>
                      
                      {/* 下载 */}
                      <td className="px-4 py-4 whitespace-nowrap" style={{ width: '90px' }}>
                        {downloadingArticles.has(article.link) ? (
                          <Button
                            onClick={(e) => e.preventDefault()}
                            size="sm"
                            className="text-xs bg-white border border-gray-200 text-gray-400 cursor-not-allowed hover:bg-white hover:border-gray-200 hover:text-gray-400 flex items-center px-3 py-2"
                          >
                            <svg className="w-2.5 h-2.5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            下载中
                          </Button>
                        ) : (
                          <Button
                            onClick={downloadedArticles.has(article.link) || article.is_deleted ? 
                              (e) => e.preventDefault() : 
                              () => downloadArticle(article.link, article.title)
                            }
                            size="sm"
                            className={`text-xs bg-white border border-gray-200 transition-colors flex items-center px-3 py-2 ${
                              downloadedArticles.has(article.link) || article.is_deleted
                                ? 'text-gray-400 cursor-not-allowed hover:bg-white hover:border-gray-200 hover:text-gray-400' 
                                : 'text-gray-700 cursor-pointer hover:bg-gray-50 hover:border-gray-300'
                            }`}
                          >
                            {downloadedArticles.has(article.link) ? (
                              <>
                                <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                                已下载
                              </>
                            ) : (
                              <>
                                <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v12m-4-4l4 4 4-4M4 17v2a1 1 0 001 1h14a1 1 0 001-1v-2" />
                                </svg>
                                下载
                              </>
                            )}
                          </Button>
                        )}
                      </td>
                      
                      {/* 导出 */}
                      <td className="px-4 py-4 whitespace-nowrap relative" style={{ width: '90px' }}>
                        {downloadedArticles.has(article.link) ? (
                          <div className="relative" data-export-dropdown>
                            <Button
                              onClick={() => {
                                if (!exportingArticles.has(article.link)) {
                                  setOpenExportDropdown(openExportDropdown === article.link ? null : article.link);
                                }
                              }}
                              className={`inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5 text-xs bg-white border border-gray-200 transition-colors flex items-center px-3 py-2 ${
                                exportingArticles.has(article.link)
                                  ? 'text-gray-400 cursor-not-allowed hover:bg-white hover:border-gray-200 hover:text-gray-400'
                                  : 'text-gray-700 cursor-pointer hover:bg-gray-50 hover:border-gray-300'
                              }`}
                            >
                              {exportingArticles.has(article.link) ? (
                                <>
                                  <svg className="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                  </svg>
                                  <span>导出中</span>
                                </>
                              ) : (
                                <>
                                  <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                  </svg>
                                  <span>导出</span>
                                  <svg 
                                    className={`w-2.5 h-2.5 transform transition-transform duration-200 ${
                                      openExportDropdown === article.link ? 'rotate-180' : 'rotate-0'
                                    }`} 
                                    fill="currentColor" 
                                    viewBox="0 0 20 20"
                                  >
                                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                  </svg>
                                </>
                              )}
                            </Button>
                            
                            {openExportDropdown === article.link && !exportingArticles.has(article.link) && (
                              <div className="absolute top-full left-0 mt-1 w-28 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                                <div className="py-1">
                                  <button
                                    onClick={() => {
                                      handleSingleExport(article, 'TXT');
                                      setOpenExportDropdown(null);
                                    }}
                                    className="cursor-pointer flex items-center w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-100"
                                  >
                                    <svg className="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    TXT
                                  </button>
                                  <button
                                    onClick={() => {
                                      handleSingleExport(article, 'HTML');
                                      setOpenExportDropdown(null);
                                    }}
                                    className="cursor-pointer flex items-center w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-100"
                                  >
                                    <svg className="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                    </svg>
                                    HTML
                                  </button>
                                  <button
                                    onClick={() => {
                                      handleSingleExport(article, 'MD');
                                      setOpenExportDropdown(null);
                                    }}
                                    className="cursor-pointer flex items-center w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-100"
                                  >
                                    <svg className="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                    </svg>
                                    MD
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <Button
                            onClick={(e) => e.preventDefault()}
                            size="sm"
                            className="text-xs bg-white border border-gray-200 text-gray-400 flex items-center cursor-not-allowed opacity-50 hover:bg-white hover:border-gray-200 hover:text-gray-400 hover:opacity-50 px-3 py-2"
                          >
                            <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            <span>导出</span>
                            <svg 
                              className="w-2.5 h-2.5"
                              fill="none" 
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </Button>
                        )}
                      </td>
                      
                      {/* 更多 */}
                      <td className="px-4 py-4 whitespace-nowrap" style={{ width: '60px' }}>
                        <button 
                          className="text-gray-400 hover:text-gray-600 transition-colors cursor-pointer disabled:cursor-not-allowed opacity-50"
                          disabled
                          title="更多功能开发中"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01" />
                          </svg>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              </div>
            </div>


          </div>
        )}
      </CardContent>
    </Card>
  );
}
