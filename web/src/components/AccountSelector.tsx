'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import { useApp } from '@/contexts/AppContext';
import {
  getStoredAccounts,
  removeAccountFromStorage,
} from '@/lib/accountStorage';
import { accountStorage, type StoredAccount } from '@/store';
import { AddAccountModal } from '@/components/AddAccountModal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from '@/components/ui/toast';
import { Account } from '@/types/account';

interface AccountSelectorProps {
  onAccountSelect: (account: Account | null) => void;
}

export function AccountSelector({ onAccountSelect }: AccountSelectorProps) {
  const { state } = useApp();
  const [storedAccounts, setStoredAccounts] = useState<StoredAccount[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<StoredAccount | null>(
    null
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 过滤账号列表
  const filteredAccounts = useMemo(() => {
    if (!searchTerm.trim()) return storedAccounts;
    return storedAccounts.filter((account) =>
      account.nickname.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [storedAccounts, searchTerm]);

  // 加载本地存储的公众号，并同步当前选择状态
  useEffect(() => {
    const loadAccounts = async () => {
      if (!state.user?.id) return;

      try {
        const accounts = await accountStorage.getAccountsByUser(state.user.id);
        setStoredAccounts(accounts);

        // 如果全局状态中有当前选择的公众号，同步到本地选择状态
        if (state.selectedAccount) {
          const matchingAccount = accounts.find(
            (acc) => acc.fakeid === state.selectedAccount?.fakeid
          );
          if (matchingAccount) {
            setSelectedAccount(matchingAccount);
          }
        }
      } catch (error) {
        console.error('Failed to load accounts:', error);
      }
    };

    loadAccounts();
  }, [state.user?.id, state.selectedAccount]);

  // 同步全局当前选择的公众号到本地选择状态
  useEffect(() => {
    if (state.selectedAccount) {
      const matchingAccount = storedAccounts.find(
        (acc) => acc.fakeid === state.selectedAccount?.fakeid
      );
      if (
        matchingAccount &&
        selectedAccount?.fakeid !== matchingAccount.fakeid
      ) {
        setSelectedAccount(matchingAccount);
      }
    } else {
      // 如果全局状态清空了选择，也清空本地选择
      if (selectedAccount) {
        setSelectedAccount(null);
      }
    }
  }, [state.selectedAccount, storedAccounts, selectedAccount]);

  // 点击外部关闭下拉框
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
        // 点击外部时，清空搜索文本恢复显示选中的账号
        setSearchTerm('');
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleInputBlur = () => {
    // 延迟处理，让点击选择事件先执行
    setTimeout(() => {
      // 无论是否有选中的账号，失焦后都清空搜索文本
      setSearchTerm('');
      setIsDropdownOpen(false);
    }, 150);
  };

  const handleSelectAccount = (account: StoredAccount) => {
    // 如果选择的是当前已选中的账号，直接关闭下拉菜单，不触发加载
    if (selectedAccount?.fakeid === account.fakeid) {
      setIsDropdownOpen(false);
      setSearchTerm(''); // 清空搜索
      return;
    }

    setSelectedAccount(account);
    setIsDropdownOpen(false);
    setSearchTerm(''); // 选择后清空搜索
    onAccountSelect(account);
  };

  const handleRemoveAccount = async (
    account: StoredAccount,
    e: React.MouseEvent
  ) => {
    e.stopPropagation(); // 防止触发选择

    if (!state.user?.id) return;

    try {
      await removeAccountFromStorage(account.fakeid, state.user.id);
      const updatedAccounts = storedAccounts.filter(
        (a) => a.fakeid !== account.fakeid
      );
      setStoredAccounts(updatedAccounts);
      setStoredAccounts(updatedAccounts);

      // 如果删除的是当前选中的账号，清空选择
      if (selectedAccount?.fakeid === account.fakeid) {
        setSelectedAccount(null);
        onAccountSelect(null);
      }

      toast.success('删除成功', `已从本地移除账号 ${account.nickname}`);
    } catch (error) {
      toast.error(
        '删除失败',
        error instanceof Error ? error.message : '未知错误'
      );
    }
  };

  const handleAccountAdded = async () => {
    if (!state.user?.id) return;

    try {
      const accounts = await getStoredAccounts(state.user.id);
      setStoredAccounts(accounts);
    } catch (error) {
      console.error('Failed to reload accounts:', error);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="flex space-x-2">
        {/* 公众号选择下拉框 */}
        <div className="flex-1 relative">
          <Input
            placeholder={
              selectedAccount ? selectedAccount.nickname : '请输入或选择公众号'
            }
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setIsDropdownOpen(true)}
            onBlur={handleInputBlur}
            className={`h-[42px] pr-8 border-gray-200 focus:ring-0 focus:outline-none focus:border-gray-300 focus-visible:border-gray-300 focus-visible:ring-0 hover:border-gray-300 transition-all duration-200 ${selectedAccount ? 'pl-12 placeholder:text-gray-900 focus:placeholder:text-gray-500' : 'placeholder:text-gray-500'}`}
            autoComplete="off"
          />

          {/* 选中公众号的头像 */}
          {selectedAccount && selectedAccount.round_head_img && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <img
                src={selectedAccount.round_head_img}
                alt={selectedAccount.nickname}
                className="w-6 h-6 rounded-full"
                referrerPolicy="no-referrer"
              />
            </div>
          )}

          {/* 下拉箭头图标 */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <svg
              className={`w-5 h-5 text-gray-400 transition-transform ${
                isDropdownOpen ? 'transform rotate-180' : ''
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>

          {/* 下拉菜单 */}
          {isDropdownOpen && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-100 rounded-lg shadow-2xl max-h-80 overflow-hidden backdrop-blur-sm">
              {storedAccounts.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <div>
                    <svg
                      className="w-8 h-8 mx-auto mb-2 text-gray-300"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1}
                        d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
                      />
                    </svg>
                    <p className="text-sm">暂无公众号</p>
                    <p className="text-xs text-gray-400 mt-1">
                      点击右侧按钮添加公众号
                    </p>
                  </div>
                </div>
              ) : (
                <div className="max-h-64 overflow-y-auto">
                  {filteredAccounts.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      <p className="text-sm">未找到匹配的公众号</p>
                      <p className="text-xs text-gray-400 mt-1">
                        请尝试其他关键词
                      </p>
                    </div>
                  ) : (
                    <>
                      {searchTerm && (
                        <div className="px-3 py-2 text-xs text-gray-500 border-b border-gray-50">
                          找到 {filteredAccounts.length} 个公众号
                        </div>
                      )}
                      {filteredAccounts.map((account) => (
                        <div
                          key={account.fakeid}
                          onClick={() => handleSelectAccount(account)}
                          className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex items-center space-x-3 flex-1 min-w-0">
                            {/* 选中状态的打勾图标 */}
                            <div className="w-4 h-4 flex-shrink-0">
                              {selectedAccount?.fakeid === account.fakeid && (
                                <svg
                                  className="w-4 h-4 text-blue-600"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                              )}
                            </div>
                            {account.round_head_img && (
                              <img
                                src={account.round_head_img}
                                alt={account.nickname}
                                className="w-8 h-8 rounded-full flex-shrink-0"
                                referrerPolicy="no-referrer"
                              />
                            )}
                            <div className="min-w-0 flex-1">
                              <div className="flex items-center space-x-2">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                  {account.nickname}
                                </p>
                                <span
                                  className={`inline-flex px-1.5 py-0.5 text-xs font-medium rounded border ${
                                    account.service_type === 1
                                      ? 'bg-blue-50 text-blue-700 border-blue-200'
                                      : account.service_type === 2
                                        ? 'bg-green-50 text-green-700 border-green-200'
                                        : 'bg-gray-50 text-gray-600 border-gray-200'
                                  }`}
                                >
                                  {account.service_type === 1
                                    ? '订阅号'
                                    : account.service_type === 2
                                      ? '服务号'
                                      : '其他'}
                                </span>
                              </div>
                              <div className="text-xs text-gray-500 truncate mt-1">
                                ID: {account.fakeid}
                              </div>
                            </div>
                          </div>
                          <button
                            onClick={(e) => handleRemoveAccount(account, e)}
                            className="p-1 text-gray-400 hover:text-red-500 transition-colors cursor-pointer"
                            title="移除此账号"
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 添加公众号按钮 */}
        <Button
          onClick={() => setShowAddModal(true)}
          className="h-[42px] px-4 bg-green-50 border border-green-200 hover:bg-green-100 hover:border-green-300 text-green-700 whitespace-nowrap transition-all duration-200 cursor-pointer"
        >
          <div className="flex items-center space-x-2">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
            <span>添加公众号</span>
          </div>
        </Button>
      </div>

      {/* 添加公众号模态框 */}
      <AddAccountModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAccountAdded={handleAccountAdded}
      />
    </div>
  );
}
