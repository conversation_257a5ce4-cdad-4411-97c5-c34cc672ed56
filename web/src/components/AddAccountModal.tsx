'use client';

import { useState, useEffect, useCallback } from 'react';
import { useApp } from '@/contexts/AppContext';
import { wechatApi } from '@/lib/api';
import { addAccountToStorage, getStoredAccounts } from '@/lib/accountStorage';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from '@/components/ui/toast';
import { Account } from '@/types/account';

interface AddAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccountAdded: (account: Account) => void;
}

export function AddAccountModal({
  isOpen,
  onClose,
  onAccountAdded,
}: AddAccountModalProps) {
  const { state } = useApp();
  const [keyword, setKeyword] = useState('');
  const [searchResults, setSearchResults] = useState<Account[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isAdding, setIsAdding] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);
  const [existingAccountIds, setExistingAccountIds] = useState<Set<string>>(
    new Set()
  );

  // 加载已存在的账号ID列表
  const loadExistingAccountIds = useCallback(async () => {
    if (!state.user?.id) return;

    try {
      const storedAccounts = await getStoredAccounts(state.user.id);
      setExistingAccountIds(
        new Set(storedAccounts.map((account) => account.fakeid))
      );
    } catch (error) {
      console.error('Failed to load existing accounts:', error);
    }
  }, [state.user?.id]);

  useEffect(() => {
    if (isOpen) {
      loadExistingAccountIds();
      // 重置状态
      setKeyword('');
      setSearchResults([]);
      setHasSearched(false);
      setIsAdding(null);
    }
  }, [isOpen, loadExistingAccountIds]);

  // 禁止背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // 自动搜索函数
  const handleSearch = useCallback(
    async (searchKeyword: string) => {
      if (!searchKeyword.trim() || !state.user) {
        setSearchResults([]);
        setHasSearched(false);
        return;
      }

      setIsSearching(true);
      setHasSearched(false);
      try {
        const result = await wechatApi.searchAccounts(
          searchKeyword,
          state.user.token
        );
        setSearchResults(result.list || []);
        setHasSearched(true);
      } catch {
        toast.error('搜索失败', '请检查网络连接');
        setSearchResults([]);
        setHasSearched(true);
      } finally {
        setIsSearching(false);
      }
    },
    [state.user]
  );

  const handleAddAccount = async (account: Account) => {
    if (!state.user?.id) {
      toast.error('添加失败', '请先登录');
      return;
    }

    // 检查是否已存在
    if (existingAccountIds.has(account.fakeid)) {
      toast.info('公众号已存在', `${account.nickname} 已在列表中`);
      return;
    }

    setIsAdding(account.fakeid);
    try {
      const success = await addAccountToStorage(account, state.user.id);
      if (success) {
        setExistingAccountIds((prev) => new Set([...prev, account.fakeid]));
        onAccountAdded(account);
        toast.success('添加成功', `${account.nickname} 已添加`);
      } else {
        toast.error('添加失败', '请重试');
      }

      // 不再自动关闭弹窗，让用户可以继续添加其他公众号
    } catch (error) {
      toast.error(
        '添加失败',
        error instanceof Error ? error.message : '请重试'
      );
    } finally {
      setIsAdding(null);
    }
  };

  // 处理输入框回车事件和ESC关闭
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && keyword.trim()) {
      handleSearch(keyword);
    } else if (e.key === 'Escape') {
      handleClose();
    }
  };

  // 添加全局ESC键监听
  useEffect(() => {
    if (!isOpen) return;

    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [isOpen]);

  // 处理输入变化，实时搜索
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKeyword(value);

    // 如果输入为空，清空搜索结果
    if (!value.trim()) {
      setSearchResults([]);
      setHasSearched(false);
    }
  };

  const handleClose = () => {
    setKeyword('');
    setSearchResults([]);
    setIsAdding(null);
    setHasSearched(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[100] flex items-start justify-center pt-[15vh]">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black/20 backdrop-blur-sm"
        onClick={handleClose}
      ></div>
      <div className="bg-white rounded-lg shadow-xl w-full max-w-xl mx-4 max-h-[70vh] relative z-10 border border-gray-200/60 flex flex-col overflow-hidden">
        {/* 简化的搜索区域 */}
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <Input
              placeholder="搜索公众号..."
              value={keyword}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              className="w-full h-11 pl-10 pr-16 text-sm border border-gray-300 bg-transparent focus:ring-0 focus:outline-none focus:border-gray-400 focus-visible:ring-0 focus-visible:ring-transparent focus-visible:border-gray-400 placeholder:text-gray-400 rounded-md"
              autoFocus
            />
            {/* 搜索图标 */}
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              {isSearching ? (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-gray-600"></div>
              ) : (
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              )}
            </div>
            {/* 简化的回车提示 */}
            {keyword.trim() && !isSearching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 px-2 py-0.5 bg-gray-100 rounded text-xs text-gray-500 font-medium">
                ↵
              </div>
            )}
          </div>
        </div>

        {/* 搜索结果 */}
        <div className="flex-1 overflow-y-auto min-h-0">
          {!hasSearched && searchResults.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500">
              <svg
                className="w-12 h-12 mb-3 text-gray-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <p className="text-sm font-medium mb-1">搜索公众号</p>
              <p className="text-xs text-gray-400">输入名称开始搜索</p>
            </div>
          ) : hasSearched && searchResults.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500">
              <svg
                className="w-12 h-12 mb-3 text-gray-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <p className="text-sm font-medium mb-1">未找到结果</p>
              <p className="text-xs text-gray-400">尝试其他关键词</p>
            </div>
          ) : (
            <>
              {/* 搜索结果列表 */}
              <div className="py-2">
                {searchResults.map((account, index) => {
                  const isExisting = existingAccountIds.has(account.fakeid);
                  return (
                    <div
                      key={account.fakeid}
                      className="group flex items-center justify-between mx-2 mb-1 p-3 rounded-md hover:bg-gray-50 transition-colors duration-150 cursor-pointer"
                    >
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        {/* 头像 */}
                        {account.round_head_img && (
                          <img
                            src={account.round_head_img}
                            alt={account.nickname}
                            className="w-8 h-8 rounded-full flex-shrink-0"
                            referrerPolicy="no-referrer"
                          />
                        )}

                        {/* 信息 */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-0.5">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {account.nickname}
                            </h4>
                            <span
                              className={`inline-flex px-1.5 py-0.5 text-xs font-medium rounded border ${
                                account.service_type === 1
                                  ? 'bg-blue-50 text-blue-700 border-blue-200'
                                  : account.service_type === 2
                                    ? 'bg-green-50 text-green-700 border-green-200'
                                    : 'bg-gray-50 text-gray-600 border-gray-200'
                              }`}
                            >
                              {account.service_type === 1
                                ? '订阅号'
                                : account.service_type === 2
                                  ? '服务号'
                                  : '其他'}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            ID: {account.fakeid}
                          </div>
                        </div>
                      </div>

                      {/* 添加按钮 */}
                      <Button
                        onClick={() => handleAddAccount(account)}
                        disabled={isExisting || isAdding === account.fakeid}
                        size="sm"
                        className={`ml-3 h-7 w-7 p-0 rounded-md transition-all duration-200 cursor-pointer ${
                          isExisting
                            ? 'bg-green-100 text-green-600 cursor-not-allowed'
                            : isAdding === account.fakeid
                              ? 'bg-blue-100 text-blue-600 cursor-not-allowed'
                              : 'bg-blue-100 hover:bg-blue-200 text-blue-600'
                        }`}
                      >
                        {isAdding === account.fakeid ? (
                          <div className="animate-spin rounded-full h-3 w-3 border-2 border-blue-300 border-t-blue-600"></div>
                        ) : isExisting ? (
                          <svg
                            className="w-3 h-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2.5}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        ) : (
                          <svg
                            className="w-3 h-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 4v16m8-8H4"
                            />
                          </svg>
                        )}
                      </Button>
                    </div>
                  );
                })}
              </div>
            </>
          )}
        </div>

        {/* 底部统计和提示 - 始终固定在底部 */}
        {hasSearched && (
          <div className="flex-shrink-0 flex items-center justify-between px-4 py-2.5 bg-gray-50/50 border-t border-gray-100/80 text-xs">
            <div className="flex items-center space-x-1.5 text-gray-600">
              <svg
                className="w-3.5 h-3.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              <span>
                {searchResults.length > 0
                  ? `找到 ${searchResults.length} 个公众号`
                  : '未找到公众号'}
              </span>
            </div>
            <div className="flex items-center space-x-1 text-gray-500">
              <kbd className="px-1.5 py-0.5 bg-white border border-gray-200 rounded text-xs font-mono">
                ESC
              </kbd>
              <span>关闭</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
