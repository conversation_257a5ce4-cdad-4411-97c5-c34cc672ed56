'use client';

import { useState, useMemo, useEffect } from 'react';
import { useApp } from '@/contexts/AppContext';
import { clearAuthData } from '@/lib/auth';
import { Button } from '@/components/ui/button';
import { LoginModal } from '@/components/LoginModal';
import { useCountdown } from '@/hooks/useCountdown';
import { userSettingsStorage } from '@/store/user-settings';

export function Header() {
  const { state, dispatch } = useApp();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showProxyDropdown, setShowProxyDropdown] = useState(false);
  const [currentProxyUrls, setCurrentProxyUrls] = useState<string[]>([]);
  const [savedProxyUrls, setSavedProxyUrls] = useState<string[]>([]);

  // 基于数据库中已保存的配置来显示状态
  const hasProxyConfigured = savedProxyUrls.length > 0 && savedProxyUrls.some(url => url.trim().length > 0);
  // 检查当前输入是否有改动
  const hasUnsavedChanges = JSON.stringify(currentProxyUrls.filter(url => url.trim() !== '')) !== JSON.stringify(savedProxyUrls);

  // 从数据库加载代理设置
  useEffect(() => {
    const loadProxySettings = async () => {
      if (state.user?.id) {
        try {
          const settings = await userSettingsStorage.getProxySettings(
            state.user.id
          );
          const proxies = settings.proxies || [];
          const proxyUrls = proxies.map(proxy => proxy.url); // 提取URL用于UI显示
          setSavedProxyUrls(proxyUrls);
          // 初始化输入框的值，如果没有配置则至少显示一个空输入框
          setCurrentProxyUrls(proxyUrls.length > 0 ? [...proxyUrls] : ['']);
        } catch (error) {
          console.error('加载代理设置失败:', error);
          setSavedProxyUrls([]);
          setCurrentProxyUrls(['']);
        }
      }
    };

    loadProxySettings();
  }, [state.user?.id]);

  // 当用户登录时初始化下载池
  useEffect(() => {
    const initializePool = async () => {
      if (state.user?.id) {
        try {
          const { initUserPool } = await import('@/lib/pool');
          await initUserPool(state.user.id);
          console.log(`🚀 用户 ${state.user.id} 的下载池已初始化`);
        } catch (error) {
          console.warn(`用户 ${state.user.id} 的下载池初始化失败:`, error);
        }
      }
    };

    initializePool();
  }, [state.user?.id]);

  // 更新代理URL列表
  const handleProxyUrlChange = (index: number, url: string) => {
    const newUrls = [...currentProxyUrls];
    newUrls[index] = url;
    setCurrentProxyUrls(newUrls);
  };

  // 添加新的代理URL
  const addProxyUrl = () => {
    // 只有在最后一个输入框不为空时才添加新的空输入框
    const lastUrl = currentProxyUrls[currentProxyUrls.length - 1];
    if (lastUrl && lastUrl.trim() !== '') {
      setCurrentProxyUrls([...currentProxyUrls, '']);
    } else if (currentProxyUrls.length === 0) {
      // 如果没有任何输入框，添加一个空输入框
      setCurrentProxyUrls(['']);
    }
    // 如果最后一个输入框已经是空的，就不添加新的
  };

  // 删除代理URL
  const removeProxyUrl = (index: number) => {
    const newUrls = currentProxyUrls.filter((_, i) => i !== index);
    setCurrentProxyUrls(newUrls);
  };

  // 保存代理设置到数据库
  const handleProxySave = async () => {
    // 过滤空字符串和无效URL
    const validUrls = currentProxyUrls
      .map(url => url.trim())
      .filter(url => url !== '');
    
    if (state.user?.id) {
      try {
        // 转换为新的格式：创建带ID的代理对象数组
        const { v4: uuid } = await import('uuid');
        const proxies = validUrls.map(url => ({
          id: uuid(),
          url: url,
          active: true
        }));
        
        await userSettingsStorage.setProxySettings(
          state.user.id,
          proxies
        );
        setSavedProxyUrls(validUrls);
        
        // 更新当前显示的代理URL列表：
        // 如果有有效的URL，就只显示这些有效的URL（不自动添加空输入框）
        // 如果没有有效URL，则显示一个空输入框供用户开始配置
        setCurrentProxyUrls(validUrls.length > 0 ? [...validUrls] : ['']);
        
        setShowProxyDropdown(false);
        
        // 发出代理配置更新事件，通知其他组件
        window.dispatchEvent(new CustomEvent('proxyConfigUpdated', {
          detail: { proxyUrls: validUrls, userId: state.user.id }
        }));
        
        console.log(`💾 代理配置已保存：${validUrls.length} 个有效地址`);
      } catch (error) {
        console.error('保存代理设置失败:', error);
      }
    }
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-proxy-dropdown]')) {
        setShowProxyDropdown(false);
      }
    };

    if (showProxyDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showProxyDropdown]);

  // 获取倒计时信息，使用useMemo避免每次渲染时创建新的时间戳
  const expireTime = useMemo(() => {
    return state.user?.expire_time || 0;
  }, [state.user?.expire_time]);
  const countdown = useCountdown(expireTime);

  const handleLogout = () => {
    // 清理用户代理池
    import('@/lib/pool').then(({ clearUserPool }) => {
      clearUserPool();
    });
    
    dispatch({ type: 'SET_USER', payload: null });
    dispatch({ type: 'SET_QRCODE', payload: null });
    dispatch({ type: 'SET_SELECTED_ACCOUNT', payload: null });
    clearAuthData();
  };

  // 格式化倒计时显示
  const formatCountdown = () => {
    if (countdown.isExpired) {
      return '已过期';
    }

    if (countdown.days > 0) {
      return `${countdown.days}天${countdown.hours}时${countdown.minutes}分`;
    } else if (countdown.hours > 0) {
      return `${countdown.hours}时${countdown.minutes}分`;
    } else if (countdown.minutes > 0) {
      return `${countdown.minutes}分${countdown.seconds}秒`;
    } else {
      return `${countdown.seconds}秒`;
    }
  };

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 backdrop-blur-md bg-white/70 border-b border-gray-200/50">
        <div className="mx-auto px-4 py-3 max-w-6xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-9 h-9 bg-gradient-to-br from-green-400 to-green-500 rounded-lg flex items-center justify-center shadow-sm">
                <svg
                  className="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 1024 1024"
                >
                  <path d="M386.998857 60.416c143.433143 0 272.237714 76.946286 331.556572 195.510857a44.836571 44.836571 0 0 1-80.164572 40.082286c-43.885714-87.771429-141.385143-145.993143-251.392-145.993143-152.868571 0-275.163429 111.323429-275.163428 246.491429 0 76.141714 28.672 134.948571 87.552 179.712 25.234286 19.236571 24.576 38.765714 15.945142 71.241142a275.017143 275.017143 0 0 1-5.705142 18.505143c17.773714-9.654857 22.966857-12.434286 31.890285-16.822857 24.210286-12.068571 34.816-16.091429 51.492572-14.116571 15.798857 1.974857 26.185143 2.925714 36.205714 3.437714 6.217143 0.292571 11.629714 0.438857 21.065143 0.512h22.454857c18.066286 0 33.645714 10.752 40.740571 26.112a252.050286 252.050286 0 0 1-7.314285-60.489143c0-155.355429 135.533714-279.698286 300.836571-279.698286 165.302857 0 300.836571 124.342857 300.836572 279.698286 0 75.337143-32.182857 145.554286-87.478858 197.339429 1.243429 7.606857 3.364571 16.969143 7.972572 36.059428 6.363429 26.331429 8.777143 37.888 10.459428 50.980572 4.242286 33.792 0.804571 56.832-30.208 70.948571-23.478857 10.825143-42.203429 5.12-69.997714-9.801143a594.285714 594.285714 0 0 1-49.517714-31.085714l-3.072-2.048c-29.110857-19.675429-50.688-32.621714-46.226286-32.621714h-32.768c-137.654857 0-254.683429-86.308571-289.938286-204.873143l0.512 4.388571a44.763429 44.763429 0 0 1-44.836571 44.763429H355.474286a517.778286 517.778286 0 0 1-64.731429-3.657143 218.112 218.112 0 0 0-12.214857 5.851429l-5.924571 3.072c-6.363429 3.291429-14.774857 7.826286-32.914286 17.773714l-2.340572 1.243428c-22.381714 12.141714-35.693714 19.163429-48.054857 24.868572-30.72 14.189714-43.52 17.993143-65.536 6.948571-33.133714-16.603429-33.28-39.424-24.137143-74.678857 3.218286-12.288 6.875429-22.747429 16.091429-47.616 5.412571-14.701714 8.630857-23.478857 10.752-30.208C57.417143 571.977143 22.235429 492.105143 22.235429 396.434286 22.235429 209.92 186.441143 60.416 386.925714 60.416z m320 354.011429c-117.394286 0-211.163429 86.089143-211.163428 190.171428 0 104.155429 93.769143 190.171429 211.163428 190.171429h32.768c24.502857 0 31.305143 4.096 96.329143 47.908571l1.462857 1.024c-3.437714-14.701714-5.12-23.04-6.436571-32.548571-4.242286-29.769143-2.706286-48.786286 18.432-66.267429 43.666286-35.986286 68.681143-86.381714 68.681143-140.288 0-104.082286-93.769143-190.171429-211.236572-190.171428z m100.571429 115.273142a51.2 51.2 0 1 1 0 102.4 51.2 51.2 0 0 1 0-102.4z m-191.634286 0a51.2 51.2 0 1 1 0 102.4 51.2 51.2 0 0 1 0-102.4zM479.232 252.342857a57.636571 57.636571 0 1 1 0 115.2 57.636571 57.636571 0 0 1 0-115.2z m-207.506286 0a57.563429 57.563429 0 1 1 0 115.2 57.563429 57.563429 0 0 1 0-115.2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-base font-bold text-gray-900">
                  微信公众号文章下载器
                </h1>
                <p className="text-xs text-gray-500">批量导出公众号文章</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* 代理设置 - 仅登录时显示 */}
              {state.user && (
                <div className="relative" data-proxy-dropdown>
                <Button
                  onClick={() => setShowProxyDropdown(!showProxyDropdown)}
                  variant="ghost"
                  className="flex items-center space-x-2 bg-gray-50/80 hover:bg-gray-100/80 px-3 py-2 rounded-lg transition-all cursor-pointer border border-transparent hover:border-gray-200/60"
                >
                  <span className="text-xs text-gray-500">代理配置</span>
                  <div className="flex items-center space-x-1.5">
                    <div
                      className={`w-1.5 h-1.5 rounded-full ${
                        hasProxyConfigured
                          ? 'bg-green-500'
                          : 'bg-orange-500'
                      }`}
                    ></div>
                    <span className="text-xs font-medium text-gray-700">
                      {hasProxyConfigured
                        ? `已配置 ${savedProxyUrls.length} 个`
                        : '未配置'}
                    </span>
                  </div>
                  <svg
                    className={`w-3 h-3 text-gray-400 transition-transform ${showProxyDropdown ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </Button>

                {/* 下拉菜单 */}
                {showProxyDropdown && (
                  <div className="absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg w-80 z-50 max-h-96 flex flex-col">
                    <div className="px-4 py-3 border-b border-gray-100 flex-shrink-0">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-sm font-medium text-gray-900">
                          代理配置
                        </h3>
                        {hasUnsavedChanges && (
                          <span className="text-xs text-amber-600 flex items-center space-x-1">
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            <span>有未保存的修改</span>
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        支持配置多个代理地址，系统自动并发请求
                      </p>
                    </div>

                    <div className="flex-1 overflow-hidden flex flex-col">
                      <div className="flex-1 overflow-y-auto px-4 py-3">
                        <div className="space-y-3">
                          {currentProxyUrls.map((url, index) => (
                            <div key={index} className="flex items-end space-x-2">
                              <div className="flex-1">
                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                  代理地址 {index + 1}
                                </label>
                                <input
                                  type="url"
                                  value={url}
                                  onChange={(e) => handleProxyUrlChange(index, e.target.value)}
                                  placeholder="https://domain.example.com"
                                  className="w-full px-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none hover:border-gray-300 focus:border-gray-300 transition-colors shadow-xs"
                                />
                              </div>
                              {/* 显示删除按钮的逻辑：
                                  1. 如果有多个输入框，任何输入框都可以删除
                                  2. 如果只有一个输入框，不允许删除（保持至少一个输入框） */}
                              {currentProxyUrls.length > 1 && (
                                <Button
                                  onClick={() => removeProxyUrl(index)}
                                  variant="ghost"
                                  size="icon"
                                  className="text-red-500 hover:text-red-600 hover:bg-red-50 cursor-pointer p-1 flex-shrink-0 h-8 w-8 mb-1"
                                  title="删除此代理地址"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                </Button>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="px-4 py-3 border-t border-gray-100 flex-shrink-0">
                        <div className="flex items-center justify-between">
                          {/* 只有在没有空输入框或最后一个输入框不为空时才显示添加按钮 */}
                          {currentProxyUrls.length === 0 || (currentProxyUrls.length > 0 && currentProxyUrls[currentProxyUrls.length - 1].trim() !== '') ? (
                            <div
                              onClick={addProxyUrl}
                              className="text-sm text-blue-600 hover:text-blue-800 flex items-center space-x-1 cursor-pointer transition-colors"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                              <span>添加代理地址</span>
                            </div>
                          ) : (
                            <div className="text-sm text-gray-400 flex items-center space-x-1">
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                              <span>请先填写上方输入框后再添加</span>
                            </div>
                          )}
                          
                          <div className="flex items-center justify-end">
                            <Button
                              onClick={handleProxySave}
                              variant="ghost"
                              size="sm"
                              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                                hasUnsavedChanges
                                  ? 'bg-blue-600 hover:bg-blue-700 text-white hover:text-white cursor-pointer'
                                  : 'bg-gray-400 text-white hover:text-white cursor-not-allowed'
                              }`}
                              disabled={!hasUnsavedChanges}
                            >
                              保存
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              )}

              {state.user ? (
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2 bg-gray-50/80 px-3 py-2 rounded-lg">
                    {state.user.avatar && (
                      <img
                        src={state.user.avatar}
                        alt={state.user.nickname}
                        className="w-8 h-8 rounded-full ring-2 ring-white shadow-sm"
                        referrerPolicy="no-referrer"
                      />
                    )}
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-gray-900 leading-tight">
                        {state.user.nickname}
                      </span>
                      <span
                        className={`text-xs leading-tight ${
                          countdown.isExpired
                            ? 'text-red-600'
                            : countdown.days === 0 && countdown.hours < 1
                              ? 'text-orange-600'
                              : 'text-blue-600'
                        }`}
                      >
                        {countdown.isExpired
                          ? '登录已过期'
                          : `登录有效期剩余：${formatCountdown()}`}
                      </span>
                    </div>
                  </div>
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    size="sm"
                    className="border-red-200 bg-red-50 hover:bg-red-100 hover:border-red-300 text-red-700 hover:text-red-700 transition-colors cursor-pointer"
                  >
                    退出登录
                  </Button>
                </div>
              ) : (
                <Button
                  onClick={() => setShowLoginModal(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white transition-all cursor-pointer shadow-sm hover:shadow-md px-4 py-2 font-medium"
                  size="sm"
                >
                  登录
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
      />
    </>
  );
}
