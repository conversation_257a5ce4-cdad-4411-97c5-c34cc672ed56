import { AppProvider } from '@/contexts/AppContext';
import { ArticleManager, ClientOnly } from '@/components';
import { Header } from '@/components/Header';

function HomePage() {
  return (
    <div
      className="min-h-screen bg-gray-50 flex flex-col"
      suppressHydrationWarning
    >
      {/* 顶部标题栏 */}
      <Header />

      {/* 主要内容区域 - flex-1 使其占据剩余空间 */}
      <main className="flex-1 container mx-auto px-4 pt-20 pb-8">
        <div className="max-w-7xl mx-auto">
          <ClientOnly
            fallback={
              <div className="bg-white rounded-lg shadow-sm p-6 text-center">
                加载中...
              </div>
            }
          >
            <ArticleManager />
          </ClientOnly>
        </div>
      </main>

      {/* 底部信息 - 移除 mt-12，因为 flex 会自动处理间距 */}
      <footer className="bg-white border-t">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center text-sm text-gray-500">
            <p>© 2025 微信公众号文章下载器</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default function Home() {
  return (
    <AppProvider>
      <HomePage />
    </AppProvider>
  );
}
