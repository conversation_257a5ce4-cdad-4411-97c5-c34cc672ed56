import { openDB } from './db';

export interface StoredArticleDetail {
  url: string;
  title: string;
  file: Blob;
  fakeid: string;
}

class ArticleDetailStorage {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    this.db = await openDB();
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  // 保存文章详情
  async save(detail: StoredArticleDetail): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article_detail'], 'readwrite');
      const store = transaction.objectStore('article_detail');

      const request = store.put(detail);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 更新文章详情
  async update(url: string, updates: Partial<Omit<StoredArticleDetail, 'url'>>): Promise<void> {
    const existing = await this.get(url);
    if (!existing) {
      throw new Error('文章详情不存在');
    }

    const updated: StoredArticleDetail = {
      ...existing,
      ...updates,
    };

    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article_detail'], 'readwrite');
      const store = transaction.objectStore('article_detail');

      const request = store.put(updated);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 获取文章详情
  async get(url: string): Promise<StoredArticleDetail | null> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article_detail'], 'readonly');
      const store = transaction.objectStore('article_detail');

      const request = store.get(url);
      request.onsuccess = () => {
        const result = request.result;
        resolve(result || null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 批量获取文章详情
  async getBatch(urls: string[]): Promise<StoredArticleDetail[]> {
    const results: StoredArticleDetail[] = [];
    
    for (const url of urls) {
      const detail = await this.get(url);
      if (detail) {
        results.push(detail);
      }
    }

    return results;
  }

  // 获取账号下所有文章详情
  async getList(fakeid: string): Promise<StoredArticleDetail[]> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article_detail'], 'readonly');
      const store = transaction.objectStore('article_detail');
      const index = store.index('fakeid');

      const details: StoredArticleDetail[] = [];

      const request = index.openCursor(fakeid);
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          const result = cursor.value as StoredArticleDetail;
          details.push(result);
          cursor.continue();
        } else {
          resolve(details);
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 检查文章详情是否存在
  async has(url: string): Promise<boolean> {
    const detail = await this.get(url);
    return detail !== null;
  }

  // 删除文章详情
  async delete(url: string): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article_detail'], 'readwrite');
      const store = transaction.objectStore('article_detail');

      const request = store.delete(url);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 删除账号所有文章详情
  async deleteAccount(fakeid: string): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article_detail'], 'readwrite');
      const store = transaction.objectStore('article_detail');
      const index = store.index('fakeid');

      const request = index.openCursor(fakeid);
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve();
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 搜索文章详情（标题或内容）
  async search(
    fakeid: string,
    keyword: string,
    limit: number = 50
  ): Promise<StoredArticleDetail[]> {
    const details = await this.getList(fakeid);

    return details
      .filter(
        (detail) =>
          detail.title &&
          detail.title.toLowerCase().includes(keyword.toLowerCase())
      )
      .slice(0, limit);
  }

  // 获取统计
  async stats(fakeid?: string): Promise<{ total: number }> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article_detail'], 'readonly');
      const store = transaction.objectStore('article_detail');

      let total = 0;
      const request = fakeid
        ? store.index('fakeid').openCursor(fakeid)
        : store.openCursor();

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          total++;
          cursor.continue();
        } else {
          resolve({ total });
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 清空所有文章详情
  async clear(): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article_detail'], 'readwrite');
      const store = transaction.objectStore('article_detail');

      const request = store.clear();
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 关闭数据库
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 创建单例
const articleDetailStorage = new ArticleDetailStorage();

export { articleDetailStorage };
export default articleDetailStorage;