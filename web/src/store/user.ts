import { User } from '@/types';
import { openDB } from './db';

class UserStorage {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    this.db = await openDB();
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  // 保存用户信息
  async saveUser(user: User): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['user'], 'readwrite');
      const store = transaction.objectStore('user');

      const request = store.put(user);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 根据 id 获取用户
  async getUser(id: string): Promise<User | null> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['user'], 'readonly');
      const store = transaction.objectStore('user');

      const request = store.get(id);
      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  // 获取所有用户
  async getAllUsers(): Promise<User[]> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['user'], 'readonly');
      const store = transaction.objectStore('user');

      const request = store.getAll();
      request.onsuccess = () => resolve(request.result || []);
      request.onerror = () => reject(request.error);
    });
  }

  // 删除用户
  async removeUser(id: string): Promise<void> {
    const db = await this.ensureDB();

    return new Promise(async (resolve, reject) => {
      try {
        // 先获取用户信息以获得fakeid
        const user = await this.getUser(id);
        if (!user) {
          resolve();
          return;
        }

        const transaction = db.transaction(
          ['user', 'user_account_mapping', 'user_settings'],
          'readwrite'
        );
        const userStore = transaction.objectStore('user');
        const mappingStore = transaction.objectStore('user_account_mapping');
        const settingsStore = transaction.objectStore('user_settings');

        // 删除用户基本信息
        const deleteUserRequest = userStore.delete(id);

        deleteUserRequest.onsuccess = () => {
          // 删除用户账号映射
          const deleteMappingRequest = mappingStore.delete(id);

          deleteMappingRequest.onsuccess = () => {
            // 删除用户设置
            const deleteSettingsRequest = settingsStore.delete(user.id);

            deleteSettingsRequest.onsuccess = () => resolve();
            deleteSettingsRequest.onerror = () => resolve(); // 即使设置删除失败也继续
          };

          deleteMappingRequest.onerror = () => resolve(); // 即使映射删除失败也继续
        };

        deleteUserRequest.onerror = () => reject(deleteUserRequest.error);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 更新用户登录时间
  async updateLoginTime(id: string): Promise<void> {
    const user = await this.getUser(id);
    if (user) {
      user.login_time = Date.now();
      await this.saveUser(user);
    }
  }

  // 检查用户是否存在
  async userExists(id: string): Promise<boolean> {
    const user = await this.getUser(id);
    return user !== null;
  }

  // 根据 token 查找用户（可能需要遍历所有用户）
  async getUserByToken(token: string): Promise<User | null> {
    const allUsers = await this.getAllUsers();
    return allUsers.find((user) => user.token === token) || null;
  }

  // 获取当前活跃用户（最近登录的用户）
  async getCurrentUser(): Promise<User | null> {
    const allUsers = await this.getAllUsers();
    if (allUsers.length === 0) return null;

    // 按登录时间排序，返回最近登录的用户
    const sortedUsers = allUsers
      .filter((user) => user.login_time)
      .sort((a, b) => (b.login_time || 0) - (a.login_time || 0));

    return sortedUsers[0] || allUsers[0];
  }

  // 登录用户（保存用户信息）
  async loginUser(user: User): Promise<void> {
    // API已经返回了login_time，直接保存
    await this.saveUser(user);
  }

  // 关闭数据库
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 创建单例
const userStorage = new UserStorage();

export { userStorage };
export default userStorage;
