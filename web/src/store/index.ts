// Store统一入口文件 - 管理所有IndexedDB操作

// 导出存储管理器
export { accountStorage } from './account';
export { articleStorage } from './article';
export { articleDetailStorage } from './article-detail';
export { userStorage } from './user';
export { userSettingsStorage } from './user-settings';

// 导出数据库管理相关
export { initDatabase, closeDatabase } from './db';

// 导出类型（从types文件重新导出便于使用）
export type { User } from '../types';

export type { Account } from '../types/account';

export type {
  WechatApiArticle,
  AppMsgPublishResponse,
  AppMsgEx,
  PublishPage,
} from '../types/article';

export type { StoredAccount } from './account';

export type { StoredArticle } from './article';

export type { StoredArticleDetail } from './article-detail';

export type { UserSettings } from './user-settings';

// 导入存储实例
import { accountStorage } from './account';
import { articleStorage } from './article';
import { articleDetailStorage } from './article-detail';
import { userStorage } from './user';
import { userSettingsStorage } from './user-settings';
import { initDatabase } from './db';

// 便捷的存储初始化函数
export async function initStore() {
  try {
    // 使用统一的数据库初始化
    await initDatabase();

    console.log('Store初始化完成');
  } catch (error) {
    console.error('Store初始化失败:', error);
    throw error;
  }
}

// 存储管理器集合
export const store = {
  // 账号相关
  account: accountStorage,

  // 文章相关
  article: articleStorage,

  // 文章详情相关
  articleDetail: articleDetailStorage,

  // 用户相关
  user: userStorage,

  // 用户设置相关
  userSettings: userSettingsStorage,

  // 统一初始化
  init: initStore,
};

// 默认导出store对象
export default store;
