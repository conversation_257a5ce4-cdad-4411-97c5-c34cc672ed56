export interface DBConfig {
  name: string;
  version: number;
  stores: {
    name: string;
    keyPath?: string;
    autoIncrement?: boolean;
    indexes?: {
      name: string;
      keyPath: string | string[];
      unique?: boolean;
    }[];
    // 自定义升级逻辑
    onUpgrade?: (
      store: IDBObjectStore,
      transaction: IDBTransaction,
      oldVersion: number,
      newVersion: number
    ) => void;
  }[];
  // 全局升级逻辑
  onUpgrade?: (
    db: IDBDatabase,
    transaction: IDBTransaction,
    oldVersion: number,
    newVersion: number
  ) => void;
}

class DatabaseManager {
  private db: IDBDatabase | null = null;
  private openPromise: Promise<IDBDatabase> | null = null;

  async openDatabase(config: DBConfig): Promise<IDBDatabase> {
    // 如果数据库已经打开并且连接有效，直接返回
    if (this.db && this.db.objectStoreNames) {
      return this.db;
    }

    // 如果正在打开过程中，等待打开完成
    if (this.openPromise) {
      return this.openPromise;
    }

    // 开始新的打开过程
    this.openPromise = new Promise((resolve, reject) => {
      const request = indexedDB.open(config.name, config.version);

      request.onerror = () => {
        this.openPromise = null;
        reject(request.error);
      };

      request.onsuccess = () => {
        const db = request.result;
        this.db = db;
        this.openPromise = null;

        // 监听数据库连接意外关闭
        db.onclose = () => {
          console.log('Database connection was closed unexpectedly');
          this.db = null;
        };

        resolve(db);
      };

      request.onupgradeneeded = (event) => {
        const db = request.result;
        const transaction = request.transaction!;
        const oldVersion = event.oldVersion;
        const newVersion = event.newVersion || config.version;

        config.stores.forEach((storeConfig) => {
          let store: IDBObjectStore;
          // 如果存储已存在，获取现有的；否则创建新的
          if (!db.objectStoreNames.contains(storeConfig.name)) {
            store = db.createObjectStore(storeConfig.name, {
              keyPath: storeConfig.keyPath,
              autoIncrement: storeConfig.autoIncrement,
            });
          } else {
            store = transaction.objectStore(storeConfig.name);
          }
          // 处理索引：删除旧的，创建新的
          if (storeConfig.indexes) {
            storeConfig.indexes.forEach((index) => {
              // 删除同名的旧索引（如果存在）
              if (store.indexNames.contains(index.name)) {
                store.deleteIndex(index.name);
              }
              // 创建新索引
              store.createIndex(index.name, index.keyPath, {
                unique: index.unique || false,
              });
            });
          }
          // 执行自定义store升级逻辑
          if (storeConfig.onUpgrade) {
            storeConfig.onUpgrade(store, transaction, oldVersion, newVersion);
          }
        });

        // 执行全局升级逻辑
        if (config.onUpgrade) {
          config.onUpgrade(db, transaction, oldVersion, newVersion);
        }
      };
    });

    return this.openPromise;
  }

  getDatabase(): IDBDatabase | null {
    return this.db;
  }

  closeDatabase(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
    this.openPromise = null;
  }
}

const dbManager = new DatabaseManager();

export const WX_CRAWLER_DB_CONFIG: DBConfig = {
  name: 'wx-crawler',
  version: 13,
  stores: [
    {
      name: 'user',
      keyPath: 'id',
    },
    {
      name: 'account',
      keyPath: 'fakeid',
    },
    {
      name: 'user_account_mapping',
    },
    {
      name: 'user_settings',
      keyPath: 'user_id',
    },
    {
      name: 'article',
      indexes: [
        {
          name: 'fakeid',
          keyPath: 'fakeid',
          unique: false,
        },
        {
          name: 'create_time',
          keyPath: 'create_time',
          unique: false,
        },
        {
          name: 'link',
          keyPath: 'link',
          unique: true,
        },
        {
          name: 'appmsgid',
          keyPath: 'appmsgid',
          unique: false,
        },
        {
          name: 'itemidx',
          keyPath: 'itemidx',
          unique: false,
        },
        {
          name: 'fakeid_appmsgid_itemidx',
          keyPath: ['fakeid', 'appmsgid', 'itemidx'],
          unique: false,
        },
      ],
    },
    {
      name: 'article_detail',
      keyPath: 'url',
      indexes: [
        {
          name: 'fakeid',
          keyPath: 'fakeid',
          unique: false,
        },
      ],
    },
  ],
};

export async function openDB(): Promise<IDBDatabase> {
  return dbManager.openDatabase(WX_CRAWLER_DB_CONFIG);
}

export function getDB(): IDBDatabase | null {
  return dbManager.getDatabase();
}

export async function initDatabase(): Promise<void> {
  try {
    await openDB();
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

export function closeDatabase(): void {
  dbManager.closeDatabase();
}

export { dbManager };
export default dbManager;
