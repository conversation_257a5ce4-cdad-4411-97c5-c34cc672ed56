import { Account } from '@/types/account';
import { AppMsgEx } from '@/types/article';
import { openDB } from './db';
import type { UserSettings } from './user-settings';

export interface StoredAccount extends Account {
  create_time: number;
  update_time: number;
  loaded_article_count: number;
  total_message_count: number;
  loaded_message_count: number;
  sync_completed: boolean;
}

class AccountStorage {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    this.db = await openDB();
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  // 添加账号
  async addAccount(account: Account, userId: string): Promise<string> {
    const db = await this.ensureDB();
    return new Promise(async (resolve, reject) => {
      try {
        const transaction = db.transaction(
          ['account', 'user_account_mapping'],
          'readwrite'
        );
        const accountStore = transaction.objectStore('account');
        const mappingStore = transaction.objectStore('user_account_mapping');

        // 首先检查账号是否已存在
        const checkAccountRequest = accountStore.get(account.fakeid);
        checkAccountRequest.onsuccess = () => {
          if (checkAccountRequest.result) {
            // 账号已存在，检查是否已经关联到当前用户
            const getMappingRequest = mappingStore.get(userId);
            getMappingRequest.onsuccess = () => {
              const accountList: string[] = getMappingRequest.result || [];
              if (accountList.includes(account.fakeid)) {
                // 账号已存在且已关联到当前用户，直接返回
                resolve(account.fakeid);
                return;
              } else {
                // 账号存在但未关联到当前用户，添加关联
                accountList.push(account.fakeid);
                const saveMappingRequest = mappingStore.put(
                  accountList,
                  userId
                );
                saveMappingRequest.onsuccess = () => {
                  resolve(account.fakeid);
                };
                saveMappingRequest.onerror = () =>
                  reject(saveMappingRequest.error);
              }
            };
            getMappingRequest.onerror = () => reject(getMappingRequest.error);
          } else {
            // 账号不存在，创建新账号
            const storedAccount: StoredAccount = {
              ...account,
              create_time: Date.now(),
              update_time: Date.now(),
              loaded_article_count: 0,
              total_message_count: 0,
              loaded_message_count: 0,
              sync_completed: false,
            };

            const addAccountRequest = accountStore.put(storedAccount);
            addAccountRequest.onsuccess = async () => {
              const getMappingRequest = mappingStore.get(userId);
              getMappingRequest.onsuccess = () => {
                // 直接获取账号ID数组，如果不存在则创建空数组
                const accountList: string[] = getMappingRequest.result || [];
                if (!accountList.includes(account.fakeid)) {
                  accountList.push(account.fakeid);
                }
                // 直接存储数组，key是userId，value是账号ID数组
                const saveMappingRequest = mappingStore.put(
                  accountList,
                  userId
                );
                saveMappingRequest.onsuccess = () => {
                  resolve(account.fakeid);
                };
                saveMappingRequest.onerror = () =>
                  reject(saveMappingRequest.error);
              };
              getMappingRequest.onerror = () => reject(getMappingRequest.error);
            };
            addAccountRequest.onerror = () => reject(addAccountRequest.error);
          }
        };
        checkAccountRequest.onerror = () => reject(checkAccountRequest.error);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 获取用户的账号列表
  async getAccountsByUser(userId: string): Promise<StoredAccount[]> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(
        ['user_account_mapping', 'account'],
        'readonly'
      );
      const mappingStore = transaction.objectStore('user_account_mapping');
      const accountStore = transaction.objectStore('account');
      const getMappingRequest = mappingStore.get(userId);
      getMappingRequest.onsuccess = () => {
        // 直接获取账号ID数组
        const accountIds: string[] = getMappingRequest.result || [];
        if (!accountIds || accountIds.length === 0) {
          resolve([]);
          return;
        }
        const accountList: StoredAccount[] = [];
        let loadedCount = 0;
        accountIds.forEach((fakeid) => {
          const getAccountRequest = accountStore.get(fakeid);
          getAccountRequest.onsuccess = () => {
            if (getAccountRequest.result) {
              accountList.push(getAccountRequest.result);
            }
            loadedCount++;
            if (loadedCount === accountIds.length) {
              const sortedAccounts = accountList.sort(
                (a, b) => b.create_time - a.create_time
              );
              resolve(sortedAccounts);
            }
          };
          getAccountRequest.onerror = () => {
            loadedCount++;
            if (loadedCount === accountIds.length) {
              const sortedAccounts = accountList.sort(
                (a, b) => b.create_time - a.create_time
              );
              resolve(sortedAccounts);
            }
          };
        });
      };
      getMappingRequest.onerror = () => reject(getMappingRequest.error);
    });
  }

  // 检查账号是否被其他用户引用
  private async isAccountReferencedByOthers(
    fakeid: string,
    excludeUserId: string
  ): Promise<boolean> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['user_account_mapping'], 'readonly');
      const mappingStore = transaction.objectStore('user_account_mapping');

      const cursorRequest = mappingStore.openCursor();

      cursorRequest.onsuccess = () => {
        const cursor = cursorRequest.result;
        if (cursor) {
          const userId = cursor.key as string; // key是用户id
          const accountIds: string[] = cursor.value; // value是账号ID数组

          // 检查不是当前用户，且包含要删除的账号ID
          if (
            userId !== excludeUserId &&
            accountIds &&
            accountIds.includes(fakeid)
          ) {
            resolve(true); // 被其他用户引用
            return;
          }

          cursor.continue();
        } else {
          resolve(false); // 没有被其他用户引用
        }
      };

      cursorRequest.onerror = () => reject(cursorRequest.error);
    });
  }

  // 删除账号
  async removeAccount(fakeid: string, userId: string): Promise<void> {
    const db = await this.ensureDB();

    try {
      // 首先检查账号是否被其他用户引用
      const isReferenced = await this.isAccountReferencedByOthers(
        fakeid,
        userId
      );

      return new Promise((resolve, reject) => {
        const transaction = db.transaction(
          ['account', 'user_account_mapping'],
          'readwrite'
        );
        const accountStore = transaction.objectStore('account');
        const mappingStore = transaction.objectStore('user_account_mapping');

        // 先移除用户与账号的关联关系
        const getMappingRequest = mappingStore.get(userId);

        getMappingRequest.onsuccess = () => {
          const accountIds: string[] = getMappingRequest.result || [];

          if (accountIds.length > 0) {
            const filteredIds = accountIds.filter(
              (id: string) => id !== fakeid
            );
            const saveMappingRequest = mappingStore.put(filteredIds, userId);

            saveMappingRequest.onsuccess = () => {
              // 如果账号没有被其他用户引用，则删除账号
              if (!isReferenced) {
                const deleteAccountRequest = accountStore.delete(fakeid);
                deleteAccountRequest.onsuccess = () => resolve();
                deleteAccountRequest.onerror = () =>
                  reject(deleteAccountRequest.error);
              } else {
                // 账号被其他用户引用，只删除关联关系，保留账号
                resolve();
              }
            };

            saveMappingRequest.onerror = () => reject(saveMappingRequest.error);
          } else {
            resolve();
          }
        };

        getMappingRequest.onerror = () => reject(getMappingRequest.error);
      });
    } catch (error) {
      throw error;
    }
  }

  // 更新账号统计信息
  async updateAccountStats(
    fakeid: string,
    stats: {
      loaded_article_count?: number;
      total_message_count?: number;
      loaded_message_count?: number;
      sync_completed?: boolean;
    }
  ): Promise<void> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['account'], 'readwrite');
      const accountStore = transaction.objectStore('account');
      const getAccountRequest = accountStore.get(fakeid);
      getAccountRequest.onsuccess = () => {
        const existingAccount = getAccountRequest.result as StoredAccount;
        if (!existingAccount) {
          reject(new Error(`Account with fakeid ${fakeid} not found`));
          return;
        }
        const updatedAccount: StoredAccount = {
          ...existingAccount,
          ...stats,
          update_time: Date.now(),
        };
        const putRequest = accountStore.put(updatedAccount);
        putRequest.onsuccess = () => resolve();
        putRequest.onerror = () => reject(putRequest.error);
      };
      getAccountRequest.onerror = () => reject(getAccountRequest.error);
    });
  }

  // 专门用于更新消息总数的便捷方法
  async updateMessageTotalCount(
    fakeid: string,
    totalCount: number
  ): Promise<void> {
    await this.updateAccountStats(fakeid, {
      total_message_count: totalCount,
    });
  }

  // 更新已加载消息数量（当获取前20条或更多消息时）
  async updateLoadedMessageCount(
    fakeid: string,
    loadedCount: number,
    totalCount?: number
  ): Promise<void> {
    const stats: {
      loaded_message_count: number;
      total_message_count?: number;
    } = {
      loaded_message_count: loadedCount,
    };

    // 如果提供了总数，也一起更新
    if (typeof totalCount === 'number') {
      stats.total_message_count = totalCount;
    }

    await this.updateAccountStats(fakeid, stats);
  }

  // 保存文章数据（委托给articleStorage）
  async saveArticles(
    articles: AppMsgEx[],
    accountFakeid: string
  ): Promise<void> {
    const { articleStorage } = await import('./article');

    // 直接使用articleStorage批量保存
    await articleStorage.saveBatch(accountFakeid, articles);
  }

  // 保存用户设置
  async saveUserSettings(settings: UserSettings): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['user_settings'], 'readwrite');
      const store = transaction.objectStore('user_settings');

      const request = store.put(settings);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 获取用户设置
  async getUserSettings(userId: string): Promise<UserSettings | null> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['user_settings'], 'readonly');
      const store = transaction.objectStore('user_settings');

      const request = store.get(userId);
      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  // 设置当前选择的账号
  async setSelectedAccount(
    userId: string,
    fakeid: string | null
  ): Promise<void> {
    const settings = (await this.getUserSettings(userId)) || {
      user_id: userId,
    };
    settings.selected_account_fakeid = fakeid || undefined;
    await this.saveUserSettings(settings);
  }

  // 获取当前账号选中的公众号
  async getSelectedAccount(userId: string): Promise<StoredAccount | null> {
    const settings = await this.getUserSettings(userId);
    if (!settings?.selected_account_fakeid) {
      return null;
    }

    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['account'], 'readonly');
      const store = transaction.objectStore('account');

      const request = store.get(settings.selected_account_fakeid!);
      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  // 获取公众号信息
  async getAccount(fakeid: string): Promise<StoredAccount | null> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['account'], 'readonly');
      const accountStore = transaction.objectStore('account');

      const request = accountStore.get(fakeid);
      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  // 保存当前选择的账号（简化版本，用于AppContext）
  async saveSelectedAccount(
    userId: string,
    account: StoredAccount
  ): Promise<void> {
    await this.setSelectedAccount(userId, account.fakeid);
  }

  // 关闭数据库
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 创建单例
const accountStorage = new AccountStorage();

export { accountStorage };
export default accountStorage;
