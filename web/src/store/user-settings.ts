import { openDB } from './db';

export interface UserSettings {
  user_id: string;
  selected_account_fakeid?: string;
  preferences?: Record<string, unknown>;
  theme?: 'light' | 'dark' | 'system';
  language?: string;
  create_time?: number;
  update_time?: number;
}

class UserSettingsStorage {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    this.db = await openDB();
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  // 保存用户设置
  async saveUserSettings(
    userId: string,
    settings: Partial<UserSettings>
  ): Promise<void> {
    const db = await this.ensureDB();

    return new Promise(async (resolve, reject) => {
      try {
        const transaction = db.transaction(['user_settings'], 'readwrite');
        const store = transaction.objectStore('user_settings');

        // 获取现有设置
        const getRequest = store.get(userId);

        getRequest.onsuccess = () => {
          const existingSettings: UserSettings = getRequest.result || {
            user_id: userId,
            create_time: Date.now(),
          };

          // 合并设置
          const updatedSettings: UserSettings = {
            ...existingSettings,
            ...settings,
            user_id: userId,
            update_time: Date.now(),
          };

          const putRequest = store.put(updatedSettings);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(putRequest.error);
        };

        getRequest.onerror = () => reject(getRequest.error);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 获取用户设置
  async getUserSettings(userId: string): Promise<UserSettings | null> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['user_settings'], 'readonly');
      const store = transaction.objectStore('user_settings');

      const request = store.get(userId);
      request.onsuccess = () => {
        const result = request.result;
        resolve(result || null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 获取用户设置中的特定字段
  async getSettingValue<K extends keyof UserSettings>(
    userId: string,
    key: K
  ): Promise<UserSettings[K] | null> {
    const settings = await this.getUserSettings(userId);
    return settings?.[key] || null;
  }

  // 更新用户设置中的特定字段
  async updateSettingValue<K extends keyof UserSettings>(
    userId: string,
    key: K,
    value: UserSettings[K]
  ): Promise<void> {
    await this.saveUserSettings(userId, {
      [key]: value,
    } as Partial<UserSettings>);
  }

  // 设置当前选择的账号
  async setSelectedAccount(
    userId: string,
    fakeid: string | null
  ): Promise<void> {
    await this.updateSettingValue(
      userId,
      'selected_account_fakeid',
      fakeid || undefined
    );
  }

  // 获取当前选择的账号ID
  async getSelectedAccount(userId: string): Promise<string | null> {
    const selectedAccountId = await this.getSettingValue(
      userId,
      'selected_account_fakeid'
    );
    return selectedAccountId || null;
  }

  // 设置主题
  async setTheme(
    userId: string,
    theme: 'light' | 'dark' | 'system'
  ): Promise<void> {
    await this.updateSettingValue(userId, 'theme', theme);
  }

  // 获取主题
  async getTheme(userId: string): Promise<'light' | 'dark' | 'system'> {
    const theme = await this.getSettingValue(userId, 'theme');
    return theme || 'system';
  }

  // 设置语言
  async setLanguage(userId: string, language: string): Promise<void> {
    await this.updateSettingValue(userId, 'language', language);
  }

  // 获取语言
  async getLanguage(userId: string): Promise<string> {
    const language = await this.getSettingValue(userId, 'language');
    return language || 'zh-CN';
  }

  // 设置代理配置
  async setProxySettings(
    userId: string,
    proxies: Array<{ id: string; url: string; active: boolean }>
  ): Promise<void> {
    await this.setPreference(userId, 'proxies', proxies);
  }

  // 获取代理配置
  async getProxySettings(
    userId: string
  ): Promise<{ proxies: Array<{ id: string; url: string; active: boolean }> }> {
    const proxies = (await this.getPreference(userId, 'proxies')) as Array<{ id: string; url: string; active: boolean }> || [];
    return { proxies };
  }

  // 设置自定义偏好设置
  async setPreference(
    userId: string,
    key: string,
    value: unknown
  ): Promise<void> {
    const settings = await this.getUserSettings(userId);
    const preferences = settings?.preferences || {};

    await this.saveUserSettings(userId, {
      preferences: {
        ...preferences,
        [key]: value,
      },
    });
  }

  // 获取自定义偏好设置
  async getPreference(userId: string, key: string): Promise<unknown | null> {
    const settings = await this.getUserSettings(userId);
    return settings?.preferences?.[key] || null;
  }

  // 删除用户设置
  async removeUserSettings(userId: string): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['user_settings'], 'readwrite');
      const store = transaction.objectStore('user_settings');

      const request = store.delete(userId);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 获取所有用户设置（管理员功能）
  async getAllUserSettings(): Promise<UserSettings[]> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['user_settings'], 'readonly');
      const store = transaction.objectStore('user_settings');

      const request = store.getAll();
      request.onsuccess = () => resolve(request.result || []);
      request.onerror = () => reject(request.error);
    });
  }

  // 关闭数据库
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 创建单例
const userSettingsStorage = new UserSettingsStorage();

export { userSettingsStorage };
export default userSettingsStorage;
