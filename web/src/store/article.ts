import { AppMsgEx } from '@/types/article';
import { openDB } from './db';

export interface StoredArticle extends AppMsgEx {
  fakeid: string;
}

class ArticleStorage {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    this.db = await openDB();
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  // 保存单篇文章 - 直接存储原始数据，key外部指定
  async save(fakeid: string, appmsgex: AppMsgEx): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article'], 'readwrite');
      const store = transaction.objectStore('article');

      // 在原始数据基础上添加 fakeid 列
      const dataWithFakeid = {
        ...appmsgex,
        fakeid: fakeid,
      };

      const request = store.put(dataWithFakeid, `${fakeid}:${appmsgex.aid}`);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 批量保存文章
  async saveBatch(fakeid: string, appmsgexArray: AppMsgEx[]): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article'], 'readwrite');
      const store = transaction.objectStore('article');

      let completed = 0;
      const total = appmsgexArray.length;

      if (total === 0) {
        resolve();
        return;
      }

      appmsgexArray.forEach((appmsgex) => {
        const dataWithFakeid = {
          ...appmsgex,
          fakeid: fakeid,
        };

        const request = store.put(dataWithFakeid, `${fakeid}:${appmsgex.aid}`);

        request.onsuccess = () => {
          completed++;
          if (completed === total) resolve();
        };
        request.onerror = () => reject(request.error);
      });
    });
  }

  // 获取文章
  async get(fakeid: string, aid: string): Promise<StoredArticle | null> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article'], 'readonly');
      const store = transaction.objectStore('article');

      const request = store.get(`${fakeid}:${aid}`);
      request.onsuccess = () => {
        const result = request.result;
        resolve(result || null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 获取账号下所有文章，按时间排序
  async getList(fakeid: string): Promise<StoredArticle[]> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article'], 'readonly');
      const store = transaction.objectStore('article');
      const index = store.index('fakeid');

      const articles: StoredArticle[] = [];

      const request = index.openCursor(fakeid);
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          const result = cursor.value as StoredArticle;
          articles.push(result);
          cursor.continue();
        } else {
          // 按发布时间倒序排序：先按 appmsgid 降序，如果相同则按 itemidx 升序
          articles.sort((a, b) => {
            // 先按 appmsgid 降序排序
            if (a.appmsgid !== b.appmsgid) {
              return b.appmsgid - a.appmsgid;
            }
            // appmsgid 相同时，按 itemidx 升序排序
            return a.itemidx - b.itemidx;
          });
          resolve(articles);
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 检查文章是否存在
  async has(fakeid: string, aid: string): Promise<boolean> {
    const article = await this.get(fakeid, aid);
    return article !== null;
  }

  // 删除文章
  async delete(fakeid: string, aid: string): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article'], 'readwrite');
      const store = transaction.objectStore('article');

      const request = store.delete(`${fakeid}:${aid}`);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 删除账号所有文章
  async deleteAccount(fakeid: string): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article'], 'readwrite');
      const store = transaction.objectStore('article');
      const index = store.index('fakeid');

      const request = index.openCursor(fakeid);
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve();
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 获取统计
  async stats(fakeid?: string): Promise<{ total: number }> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article'], 'readonly');
      const store = transaction.objectStore('article');

      let total = 0;
      const request = fakeid
        ? store.index('fakeid').openCursor(fakeid)
        : store.openCursor();

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          total++;
          cursor.continue();
        } else {
          resolve({ total });
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 搜索文章（简单标题搜索）
  async search(
    fakeid: string,
    keyword: string,
    limit: number = 50
  ): Promise<StoredArticle[]> {
    const articles = await this.getList(fakeid);

    return articles
      .filter(
        (article) =>
          article.title &&
          article.title.toLowerCase().includes(keyword.toLowerCase())
      )
      .slice(0, limit);
  }

  // 获取有合集的文章
  async getAlbumArticles(fakeid: string): Promise<StoredArticle[]> {
    const articles = await this.getList(fakeid);
    return articles.filter(
      (article) =>
        article.album_id &&
        article.album_id !== '0' &&
        (article.appmsg_album_infos?.length ?? 0) > 0
    );
  }

  // 按合集分组文章
  async getArticlesByAlbum(
    fakeid: string
  ): Promise<{ [albumId: string]: StoredArticle[] }> {
    const articles = await this.getList(fakeid);
    const albumGroups: { [albumId: string]: StoredArticle[] } = {};

    articles.forEach((article) => {
      if (article.album_id && article.album_id !== '0') {
        if (!albumGroups[article.album_id]) {
          albumGroups[article.album_id] = [];
        }
        albumGroups[article.album_id].push(article);
      }
    });

    return albumGroups;
  }

  // 清空所有文章
  async clear(): Promise<void> {
    const db = await this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['article'], 'readwrite');
      const store = transaction.objectStore('article');

      const request = store.clear();
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 关闭数据库
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 创建单例
const articleStorage = new ArticleStorage();

export { articleStorage };
export default articleStorage;
