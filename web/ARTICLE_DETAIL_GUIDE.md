# 文章详情保存功能使用指南

本功能参考了微信文章代理下载的实现方案，为系统添加了完整的文章详情保存功能，将文章内容解析后保存到 `article_detail` 表中。

## 功能特性

### 🚀 主要功能
- **单篇保存**：点击文章列表中的"保存详情"按钮，保存单篇文章的详细信息
- **批量保存**：选择多篇文章后，点击"批量保存详情"按钮进行批量保存
- **智能代理**：支持 Cloudflare 代理下载，解决跨域问题
- **内容解析**：自动解析文章内容、作者、发布时间、阅读量等信息
- **重复检测**：自动检测是否已保存，避免重复保存

### 📊 保存的数据
保存到 `article_detail` 表的字段包括：

**基础信息**
- `aid`: 文章唯一标识
- `title`: 文章标题
- `author`: 作者
- `digest`: 文章摘要
- `content`: 解析后的HTML内容
- `content_url`: 文章链接
- `cover`: 封面图片

**时间信息**  
- `create_time`: 创建时间
- `update_time`: 更新时间
- `publish_time`: 发布时间（解析自HTML）
- `saved_at`: 保存时间

**统计信息**
- `read_num`: 阅读量
- `like_num`: 点赞数
- `comment_id`: 评论ID

**分类信息**
- `item_show_type`: 文章类型（0=普通图文，5=视频分享等）
- `copyright_type`: 版权类型
- `copyright_stat`: 版权状态

**扩展信息**
- `ip_location`: IP属地
- `is_title_modified`: 标题是否被修改
- `media_duration`: 媒体时长（音频/视频）
- `raw_html`: 原始HTML内容

## 使用方法

### 代理配置

系统支持两种文章抓取方式：

1. **本地请求**：直接从浏览器发起请求（可能受跨域限制）
2. **远程代理**：通过 Cloudflare Worker 代理进行跨域访问

在页面右上角的"代理配置"下拉菜单中可以：
- 选择抓取方式
- 点击"代理设置"配置自定义代理地址

代理设置将保存到用户的个人设置中。

### 单篇文章保存

1. 在文章管理页面的文章列表中
2. 点击某篇文章右侧的"保存详情"按钮
3. 系统会自动下载并解析文章内容
4. 保存成功后会显示提示信息

### 批量保存

1. 在文章列表中勾选要保存的文章
2. 点击页面上方的"批量保存详情"按钮
3. 系统会逐一下载解析并保存文章
4. 显示详细的进度信息和最终结果统计

## 技术实现

### 核心组件

1. **ArticleContentParser**：HTML内容解析器
   - 提取文章标题、作者、内容
   - 清理无用的DOM元素
   - 解析发布时间、阅读量等扩展信息

2. **ArticleDetailService**：文章详情保存服务
   - 下载文章HTML内容
   - 调用解析器处理内容
   - 保存到数据库

3. **API路由**：
   - `POST /api/article-detail`: 保存文章详情
   - `GET /api/article-detail/[aid]`: 查询文章详情

### 代理支持

- 支持通过 Cloudflare Worker 代理下载
- 自动设置合适的请求头（User-Agent、Referer等）
- 解决跨域访问问题

### 错误处理

- 网络请求重试机制
- 文章删除状态检测
- 详细的错误日志和用户提示

## 界面功能

### 批量操作区域
- 显示选中文章数量
- "批量下载文章"按钮（原有功能）
- "批量保存详情"按钮（新增功能）
- 下载方式指示器（代理/本地代理）

### 文章列表
- 每行新增"保存详情"按钮
- 实时显示保存状态
- 防止重复操作

### 进度提示
- 实时显示当前处理的文章标题
- 显示完成进度（如：3/10）
- 区分成功/失败状态

## 后端 API 要求

需要后端实现以下 API 接口：

### 保存文章详情
```http
POST /api/v1/article-detail
Content-Type: application/json

{
  "aid": "string",
  "title": "string",
  "author": "string", 
  "content": "string",
  "fakeid": "string",
  // ... 其他字段
}
```

### 查询文章详情
```http
GET /api/v1/article-detail/{aid}
```

返回格式：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "aid": "string",
    "title": "string",
    // ... 完整的文章详情数据
  }
}
```

## 数据库表结构

建议的 `article_detail` 表结构：
```sql
CREATE TABLE article_detail (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  aid VARCHAR(50) NOT NULL UNIQUE,
  title TEXT NOT NULL,
  author VARCHAR(255),
  digest TEXT,
  content LONGTEXT,
  content_url TEXT,
  source_url TEXT,
  cover TEXT,
  create_time BIGINT,
  update_time BIGINT,
  item_show_type INT DEFAULT 0,
  copyright_type INT DEFAULT 0,
  copyright_stat INT DEFAULT 0,
  read_num INT DEFAULT 0,
  like_num INT DEFAULT 0,
  comment_id VARCHAR(50),
  fakeid VARCHAR(50) NOT NULL,
  publish_time VARCHAR(50),
  ip_location VARCHAR(100),
  is_title_modified BOOLEAN DEFAULT FALSE,
  media_duration VARCHAR(20),
  raw_html LONGTEXT,
  saved_at BIGINT NOT NULL,
  INDEX idx_aid (aid),
  INDEX idx_fakeid (fakeid),
  INDEX idx_saved_at (saved_at)
);
```

## 注意事项

1. **频率控制**：批量保存时会自动添加延迟（代理模式2秒，本地模式1秒）
2. **内容清理**：自动移除脚本、广告等无用元素
3. **重复检测**：保存前会检查文章是否已存在
4. **错误处理**：网络错误、解析错误都有相应的提示和日志
5. **进度反馈**：提供详细的进度信息和最终统计

这个功能完整地实现了从文章列表到详细内容保存的全流程，为后续的内容分析和管理提供了数据基础。