/**
 * 微信文章代理 - Cloudflare Workers 版本
 * 用于解决跨域问题和设置特定的请求头
 */

const UA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.0.0 Safari/537.36";

// 预设配置
const PRESETS = {
  mp: {
    Referer: "https://mp.weixin.qq.com",
  },
};

/**
 * 创建错误响应
 */
function createErrorResponse(msg, status = 400) {
  return new Response(msg, {
    status: status,
    headers: {
      "Content-Type": "text/plain",
      "Access-Control-Allow-Origin": "*",
    },
  });
}

/**
 * 解析请求参数
 */
async function parseRequest(req) {
  let targetURL = "";
  let targetMethod = "GET";
  let targetBody = "";
  let targetHeaders = {};
  let preset = "";

  const method = req.method.toLowerCase();

  if (method === "get") {
    // GET 请求：通过查询参数传递配置
    const { searchParams } = new URL(req.url);
    
    if (searchParams.has("url")) {
      targetURL = decodeURIComponent(searchParams.get("url"));
    }
    if (searchParams.has("method")) {
      targetMethod = searchParams.get("method");
    }
    if (searchParams.has("body")) {
      targetBody = decodeURIComponent(searchParams.get("body"));
    }
    if (searchParams.has("headers")) {
      try {
        targetHeaders = JSON.parse(decodeURIComponent(searchParams.get("headers")));
      } catch {
        throw new Error("Headers not valid JSON");
      }
    }
    if (searchParams.has("preset")) {
      preset = decodeURIComponent(searchParams.get("preset"));
    }
  } else if (method === "post") {
    // POST 请求：通过请求体传递配置
    try {
      const payload = await req.json();
      
      if (payload.url) {
        targetURL = payload.url;
      }
      if (payload.method) {
        targetMethod = payload.method;
      }
      if (payload.body) {
        targetBody = payload.body;
      }
      if (payload.headers) {
        targetHeaders = payload.headers;
      }
      if (payload.preset) {
        preset = payload.preset;
      }
    } catch {
      throw new Error("Invalid JSON payload");
    }
  } else if (method === "options") {
    // 处理 CORS 预检请求
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Max-Age": "86400",
      },
    });
  } else {
    throw new Error(`Method ${method.toUpperCase()} not supported`);
  }

  // 验证参数
  if (!targetURL) {
    throw new Error("URL parameter is required");
  }
  if (!/^https?:\/\//.test(targetURL)) {
    throw new Error("URL must start with http:// or https://");
  }
  if (targetMethod.toUpperCase() === "GET" && targetBody) {
    throw new Error("GET requests cannot have a body");
  }
  if (typeof targetHeaders !== "object" || targetHeaders === null) {
    throw new Error("Headers must be a valid object");
  }

  // 设置默认 User-Agent
  if (!targetHeaders["User-Agent"]) {
    targetHeaders["User-Agent"] = UA;
  }

  // 应用预设配置
  if (preset && preset in PRESETS) {
    Object.assign(targetHeaders, PRESETS[preset]);
  }

  return {
    targetURL,
    targetMethod: targetMethod.toUpperCase(),
    targetBody,
    targetHeaders,
  };
}

/**
 * 执行代理请求
 */
async function proxyFetch(url, method, body, headers = {}) {
  const requestOptions = {
    method,
    headers,
  };

  if (body && method.toUpperCase() !== "GET") {
    requestOptions.body = body;
  }

  return fetch(url, requestOptions);
}

/**
 * 主处理函数
 */
export default {
  async fetch(request, env, ctx) {
    try {
      // 处理 OPTIONS 预检请求
      if (request.method === "OPTIONS") {
        return new Response(null, {
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
            "Access-Control-Max-Age": "86400",
          },
        });
      }

      // 解析请求参数
      const {
        targetURL,
        targetMethod,
        targetBody,
        targetHeaders,
      } = await parseRequest(request);

      // 执行代理请求
      const response = await proxyFetch(
        targetURL,
        targetMethod,
        targetBody,
        targetHeaders
      );

      // 创建响应，包含必要的 CORS 头
      const responseHeaders = new Headers();
      responseHeaders.set("Access-Control-Allow-Origin", "*");
      
      // 保持原响应的 Content-Type
      const contentType = response.headers.get("Content-Type");
      if (contentType) {
        responseHeaders.set("Content-Type", contentType);
      }

      // 如果是文本类型，可以添加字符编码
      if (contentType?.includes("text/") || contentType?.includes("application/json")) {
        responseHeaders.set("Access-Control-Allow-Headers", "Content-Type");
      }

      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      });

    } catch (error) {
      console.error("Proxy error:", error);
      return createErrorResponse(
        error instanceof Error ? error.message : "Unknown error occurred"
      );
    }
  },
};