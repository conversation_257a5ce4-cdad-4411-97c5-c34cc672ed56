# 微信文章代理 - Cloudflare Workers 部署指南

## 快速部署

### 1. 创建 Cloudflare Worker

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 选择 `Workers & Pages` → `Create application` → `Create Worker`
3. 给Worker起个名字（如：`wechat-proxy`）
4. 将 `mp-proxy.js` 的内容复制到代码编辑器中
5. 点击 `Save and Deploy`

### 2. 获取Worker URL

部署成功后，你会得到一个类似这样的URL：
```
https://wechat-proxy.your-username.workers.dev
```

## 使用方法

### 基本用法（GET请求）

```bash
# 下载微信文章
curl "https://wechat-proxy.your-username.workers.dev?url=https://mp.weixin.qq.com/s/ARTICLE_ID&preset=mp"

# 在浏览器中直接访问
https://wechat-proxy.your-username.workers.dev?url=https://mp.weixin.qq.com/s/ARTICLE_ID&preset=mp
```

### JavaScript调用示例

```javascript
// 使用GET方式
const articleUrl = 'https://mp.weixin.qq.com/s/ARTICLE_ID';
const proxyUrl = `https://wechat-proxy.your-username.workers.dev?url=${encodeURIComponent(articleUrl)}&preset=mp`;

fetch(proxyUrl)
  .then(response => response.text())
  .then(html => {
    console.log('文章内容:', html);
  })
  .catch(error => {
    console.error('下载失败:', error);
  });

// 使用POST方式（更灵活）
fetch('https://wechat-proxy.your-username.workers.dev', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    url: 'https://mp.weixin.qq.com/s/ARTICLE_ID',
    preset: 'mp',
    headers: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    }
  })
})
.then(response => response.text())
.then(html => console.log(html));
```

## 参数说明

### GET请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| url | string | ✅ | 目标URL（需要URL编码） |
| method | string | ❌ | HTTP方法，默认GET |
| headers | string | ❌ | JSON格式的请求头（需要URL编码） |
| preset | string | ❌ | 预设配置，`mp`表示微信公众号 |
| body | string | ❌ | 请求体内容（需要URL编码） |

### POST请求体

```json
{
  "url": "https://mp.weixin.qq.com/s/ARTICLE_ID",
  "method": "GET",
  "preset": "mp",
  "headers": {
    "Accept": "text/html",
    "Accept-Language": "zh-CN,zh;q=0.9"
  },
  "body": ""
}
```

## 预设配置

### mp (微信公众号)
自动添加以下请求头：
- `Referer: https://mp.weixin.qq.com`
- `User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...`

## 使用示例

### 1. 直接在浏览器访问
```
https://wechat-proxy.your-username.workers.dev?url=https://mp.weixin.qq.com/s/dQw4w9WgXcQ&preset=mp
```

### 2. 在你的应用中集成

```javascript
// 下载微信文章的函数
async function downloadWechatArticle(articleUrl) {
  const proxyUrl = 'https://wechat-proxy.your-username.workers.dev';
  
  try {
    const response = await fetch(proxyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: articleUrl,
        preset: 'mp'
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.text();
  } catch (error) {
    console.error('下载文章失败:', error);
    throw error;
  }
}

// 使用示例
downloadWechatArticle('https://mp.weixin.qq.com/s/ARTICLE_ID')
  .then(html => {
    // 处理文章HTML内容
    console.log('文章下载成功');
  })
  .catch(error => {
    console.error('下载失败:', error);
  });
```

### 3. 批量下载文章

```javascript
async function batchDownloadArticles(urls) {
  const proxyUrl = 'https://wechat-proxy.your-username.workers.dev';
  const results = [];
  
  for (const url of urls) {
    try {
      const response = await fetch(`${proxyUrl}?url=${encodeURIComponent(url)}&preset=mp`);
      const html = await response.text();
      
      results.push({
        url,
        success: true,
        content: html
      });
      
      // 添加延迟避免过于频繁
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      results.push({
        url,
        success: false,
        error: error.message
      });
    }
  }
  
  return results;
}
```

## 错误处理

代理会返回以下错误：
- `400`: 参数错误（缺少URL、格式错误等）
- `500`: 服务器内部错误或目标网站无法访问

## 注意事项

1. **频率限制**：建议在批量请求时添加延迟，避免触发微信的频率限制
2. **URL编码**：GET请求中的参数需要进行URL编码
3. **CORS支持**：代理自动处理跨域问题，支持从任何域名调用
4. **免费额度**：Cloudflare Workers免费版每天有100,000次请求额度

## 自定义域名（可选）

如果你有自己的域名，可以在Cloudflare中设置自定义域名：

1. 在Worker设置中点击 `Triggers` 标签
2. 点击 `Add Custom Domain`
3. 输入你的子域名（如：`proxy.yourdomain.com`）
4. 等待DNS生效

这样你就可以使用：
```
https://proxy.yourdomain.com?url=...
```