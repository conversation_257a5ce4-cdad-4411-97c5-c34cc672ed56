# 微信文章爬虫前端

一个基于 Next.js + React + TypeScript 的微信公众号文章管理和导出工具。

## 功能特性

- 📱 **账号管理**: 支持多个微信公众号账号管理
- 📖 **文章同步**: 自动同步微信公众号文章列表
- 💾 **内容下载**: 下载完整的文章HTML内容到本地存储
- 📦 **智能导出**: 
  - 单篇文章导出为ZIP包（包含HTML和所有图片、CSS资源）
  - 批量文章导出，每篇文章独立打包资源
  - 图片本地化，完全离线可用
- 🔍 **搜索筛选**: 支持文章标题搜索和类型筛选
- 📊 **统计信息**: 显示文章总数、下载进度等统计数据

## 技术栈

- **前端框架**: Next.js 15 + React 19
- **类型系统**: TypeScript
- **样式方案**: Tailwind CSS
- **UI组件**: 自定义组件库
- **状态管理**: React Context + hooks
- **数据存储**: IndexedDB (本地存储)
- **打包工具**: JSZip (文章导出打包)
- **包管理器**: pnpm

## 项目结构

```
wx-crawler-frontend/
├── src/
│   ├── app/                 # Next.js App Router
│   ├── components/          # React 组件
│   │   ├── ui/             # 基础UI组件
│   │   ├── AccountSelector.tsx
│   │   ├── ArticleManager.tsx
│   │   └── LoginModal.tsx
│   ├── contexts/           # React Context
│   ├── lib/                # 工具库
│   │   ├── api.ts          # API接口
│   │   ├── htmlExporter.ts # HTML导出功能
│   │   ├── indexedDB.ts    # 数据库操作
│   │   └── utils.ts        # 工具函数
│   ├── store/              # 数据存储
│   └── types/              # TypeScript类型定义
├── public/                 # 静态资源
├── package.json
└── pnpm-lock.yaml         # pnpm锁定文件
```

## 开发环境设置

### 前置要求

- Node.js >= 18
- pnpm >= 8

### 安装依赖

```bash
pnpm install
```

### 环境配置

创建 `.env.local` 文件（二选一）：

方式 A（推荐）：仅配置后端服务地址，前端自动补全 `/api/v1`
```env
# 后端服务地址（不含 /api/v1）
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
```

方式 B（兼容旧版）：直接配置完整的 API 基础路径
```env
# 完整 API 基础路径（含 /api/v1）
NEXT_PUBLIC_API_BASE=http://localhost:8080/api/v1
```

### 启动开发服务器

```bash
pnpm dev
```

访问 http://localhost:3000 查看应用。

## 主要功能使用

### 1. 账号管理

- 在首页点击"添加账号"
- 输入微信公众号的相关信息
- 系统会自动验证并保存账号信息

### 2. 文章同步

- 选择要管理的账号
- 点击"同步文章"获取最新文章列表
- 支持增量同步，只获取新文章

### 3. 文章下载

- 在文章列表中点击"下载"按钮
- 系统会下载完整的HTML内容到本地
- 支持批量下载选中的文章

### 4. 文章导出

**单篇导出**：
- 点击文章操作菜单中的"ZIP包"
- 生成包含HTML和所有资源的ZIP文件
- 所有图片和CSS文件都会本地化

**批量导出**：
- 选择多篇文章
- 点击"批量导出"
- 每篇文章独立打包，生成统一的ZIP文件

### 导出文件结构

单篇文章导出：
```
文章标题.zip
├── 文章标题.html     # 主HTML文件
└── assets/           # 资源文件夹
    ├── image1.jpg    # 图片文件
    ├── image2.png
    └── style.css     # 样式文件
```

批量导出：
```
批量导出_2024-01-01.zip
├── 001_文章一/
│   ├── 文章一.html
│   └── assets/
├── 002_文章二/
│   ├── 文章二.html
│   └── assets/
└── ...
```

## 可用脚本

```bash
# 开发模式
pnpm dev

# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start

# 类型检查
pnpm type-check

# 代码格式化
pnpm lint

# 自动修复部分 Lint 问题
pnpm lint:fix
```

## 数据存储

应用使用 IndexedDB 进行本地数据存储：

- **账号信息**: 存储微信公众号账号数据
- **文章列表**: 缓存文章元数据
- **文章内容**: 存储下载的完整HTML内容
- **用户设置**: 保存用户偏好设置

## API 接口

应用通过以下主要接口与后端通信：

- `GET /api/accounts` - 获取账号列表
- `POST /api/accounts` - 添加新账号
- `GET /api/articles/:fakeid` - 获取文章列表
- `POST /api/articles/sync` - 同步文章
- `GET /api/articles/content/:url` - 获取文章内容

## 开发说明

### 代码规范

- 使用 TypeScript 进行类型安全开发
- 遵循 React Hooks 最佳实践
- 组件采用函数式组件 + hooks 模式
- 使用 ESM 导入导出语法

### 性能优化

- 文章列表使用虚拟滚动
- 图片懒加载
- IndexedDB 异步操作
- 批量操作防抖处理

## 故障排除

### 常见问题

1. **导出失败**
   - 检查网络连接
   - 确认文章已下载到本地
   - 查看浏览器控制台错误信息

2. **同步失败**
   - 验证账号信息是否正确
   - 检查API服务器连接状态

3. **图片加载失败**
   - 部分微信图片可能有防盗链
   - 导出的离线版本已处理此问题

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交修改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
