package app

import (
    "database/sql"
    "fmt"
    "os"
    "strings"

    "github.com/gin-gonic/gin"

    "wx-crawler/internal/config"
    "wx-crawler/internal/handler"
    "wx-crawler/internal/middleware"
)

// Setup 初始化路由
func Setup(db *sql.DB, cfg *config.Config) *gin.Engine {
    r := gin.New()

	// Basic middleware
	r.Use(middleware.Logger())
	r.Use(gin.Recovery())

	// Custom middleware
	r.Use(middleware.RequestID())
	r.Use(middleware.SecurityHeaders())

    // CORS middleware：优先使用配置，其次支持 CORS_ALLOWED_ORIGINS（逗号分隔）
    r.Use(func(c *gin.Context) {
        origin := c.Request.Header.Get("Origin")

        // 基于配置的允许列表
        allowedOrigins := append([]string{}, cfg.CORS.AllowedOrigins...)

        // 兼容环境变量覆盖（逗号分隔）
        if raw := os.Getenv("CORS_ALLOWED_ORIGINS"); strings.TrimSpace(raw) != "" {
            parts := strings.Split(raw, ",")
            allowedOrigins = allowedOrigins[:0]
            for _, p := range parts {
                p = strings.TrimSpace(p)
                if p != "" {
                    allowedOrigins = append(allowedOrigins, p)
                }
            }
        }

        // 检查是否在允许的域名列表中
        allowed := false
        for _, allowedOrigin := range allowedOrigins {
            if origin == allowedOrigin {
                allowed = true
                break
            }
        }

        // 允许的方法与头来源于配置
        allowMethods := strings.Join(cfg.CORS.AllowedMethods, ", ")
        allowHeaders := strings.Join(cfg.CORS.AllowedHeaders, ", ")

        if allowed {
            c.Header("Access-Control-Allow-Origin", origin)
            c.Header("Access-Control-Allow-Credentials", fmt.Sprintf("%t", cfg.CORS.AllowCredentials))
        } else {
            c.Header("Access-Control-Allow-Origin", "*")
            c.Header("Access-Control-Allow-Credentials", "false")
        }

        c.Header("Access-Control-Allow-Methods", allowMethods)
        c.Header("Access-Control-Allow-Headers", allowHeaders)

        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }

        c.Next()
    })

	// 前端在独立项目中部署；此处不再提供静态资源路由

	// API version group
	v1 := r.Group("/api/v1")
	{
		// Health check
		healthHandler := handler.NewHealthHandler(db)
		v1.GET("/health", healthHandler.Hello)

		// WeChat API routes
		wechatHandler := handler.NewWechatHandler()
		wechat := v1.Group("/wechat")
		{
			wechat.GET("/session", wechatHandler.StartSession)
			wechat.GET("/qrcode", wechatHandler.GetQRCode)
			wechat.GET("/scan", wechatHandler.ScanStatus)
			wechat.GET("/login", wechatHandler.Login)
			wechat.GET("/search-account", wechatHandler.SearchAccount)
			wechat.GET("/search-article", wechatHandler.SearchArticle)
			wechat.GET("/download-article", wechatHandler.DownloadArticle)
		}
	}

    return r
}
