package handler

import (
	"database/sql"
	"log/slog"
	"time"

	"github.com/gin-gonic/gin"

	"wx-crawler/internal/constants"
	"wx-crawler/internal/middleware"
	"wx-crawler/internal/model"
	"wx-crawler/internal/errcode"
	"wx-crawler/internal/response"
)

type HealthHandler struct {
	db *sql.DB
}

func NewHealthHandler(db *sql.DB) *HealthHandler {
	return &HealthHandler{db: db}
}

// Hello 健康检查接口
// 检查服务器和数据库连接状态
func (h *HealthHandler) Hello(c *gin.Context) {
	requestID := middleware.GetRequestID(c)

	status := "healthy"
	if h.db != nil {
		if err := h.db.Ping(); err != nil {
			slog.Error("Database health check failed",
				slog.String("request_id", requestID),
				slog.String("error", err.Error()),
			)
			response.ErrorWithCode(c, errcode.DatabaseConnectFailed, err.Error())
			return
		}
	} else {
		status = "healthy_without_db"
	}

	slog.Info("Health check OK",
		slog.String("request_id", requestID),
		slog.String("status", status),
	)

	response.Success(c, model.HealthData{
		Status:    status,
		Timestamp: time.Now().Unix(),
		Version:   constants.AppVersion,
	})
}
