package handler

import (
	"bytes"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"math/big"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/gin-gonic/gin"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"wx-crawler/internal/constants"
	"wx-crawler/internal/model"
	"wx-crawler/internal/errcode"
	"wx-crawler/internal/response"
)

type WechatHandler struct {
	httpClient *http.Client
}

type CookieItem struct {
	Name     string `json:"name"`
	Value    string `json:"value"`
	Path     string `json:"path"`
	Expires  string `json:"expires"`
	Secure   bool   `json:"secure"`
	HttpOnly bool   `json:"httpOnly"`
}

func NewWechatHandler() *WechatHandler {
	return &WechatHandler{
		httpClient: defaultHTTPClient,
	}
}

// 统一的上游 HTTP 客户端
var defaultHTTPClient = &http.Client{Timeout: constants.DefaultTimeout * time.Second}

func (w *WechatHandler) StartSession(c *gin.Context) {
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	randInt, _ := rand.Int(rand.Reader, big.NewInt(90))
	sessionid := fmt.Sprintf("%d%d", timestamp, randInt.Int64()+10)
	data := url.Values{
		"userlang":     {"zh_CN"},
		"redirect_url": {""},
		"login_type":   {"3"},
		"sessionid":    {sessionid},
		"token":        {""},
		"lang":         {"zh_CN"},
		"f":            {"json"},
		"ajax":         {"1"},
	}

	// 创建请求
	req, err := http.NewRequest(
		http.MethodPost,
		"https://mp.weixin.qq.com/cgi-bin/bizlogin?action=startlogin",
		bytes.NewBufferString(data.Encode()),
	)
	if err != nil {
		slog.Error("create request failed", slog.String("error", err.Error()))
		response.RequestError(c, err.Error())
		return
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Referer", constants.WechatReferer)
	req.Header.Set("Origin", constants.WechatOrigin)
	req.Header.Set("User-Agent", constants.DefaultUserAgent)
	resp, err := defaultHTTPClient.Do(req)
	if err != nil {
		slog.Error("start_session upstream request failed", slog.String("error", err.Error()))
		response.ErrorWithCode(c, errcode.WechatTimeout, err.Error())
		return
	}
	defer func(Body io.ReadCloser) {
		if err := Body.Close(); err != nil {
			slog.Debug("close body error", slog.String("op", "start_session"), slog.String("error", err.Error()))
		}
	}(resp.Body)

	// 将微信返回的 Set-Cookie 设置到返回结果中
	for _, sc := range resp.Header.Values("Set-Cookie") {
		c.Writer.Header().Add("Set-Cookie", sc)
	}
	// 微信返回内容
	wxData, _ := io.ReadAll(resp.Body)
	wxDataStr := string(wxData)
	var wxDataJson map[string]interface{}
	if err := json.Unmarshal([]byte(wxDataStr), &wxDataJson); err != nil {
		slog.Warn("start_session json unmarshal failed")
		response.JSONError(c, err.Error())
		return
	}

	response.Success(c, wxDataJson)
}

func (w *WechatHandler) GetQRCode(c *gin.Context) {
	// 生成随机时间戳
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	// 创建请求 URL 和参数
	params := url.Values{}
	params.Set("action", "getqrcode")
	params.Set("random", strconv.FormatInt(timestamp, 10))

	// 创建请求
	req, err := http.NewRequest(
		http.MethodGet,
		"https://mp.weixin.qq.com/cgi-bin/scanloginqrcode?"+params.Encode(),
		nil,
	)
	if err != nil {
		slog.Error("create request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:    http.StatusInternalServerError,
			Message: "创建请求失败",
			Data:    nil,
		})
		return
	}

	// 设置 Cookie
	for _, cookie := range c.Request.Cookies() {
		req.AddCookie(&http.Cookie{Name: cookie.Name, Value: cookie.Value})
	}
	req.Header.Set("Referer", constants.WechatReferer)
	req.Header.Set("Origin", constants.WechatOrigin)
	req.Header.Set("User-Agent", constants.DefaultUserAgent)

	// 发送请求
	resp, err := defaultHTTPClient.Do(req)
	if err != nil {
		slog.Error("get_qrcode upstream request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusBadGateway, model.Response{
			Code:      http.StatusBadGateway,
			Message:   "上游服务不可用",
			ErrorCode: constants.BizErrUpstream,
			Data:      nil,
		})
		return
	}
	defer func(Body io.ReadCloser) {
		if err := Body.Close(); err != nil {
			slog.Debug("close body error", slog.String("op", "get_qrcode"), slog.String("error", err.Error()))
		}
	}(resp.Body)

	// 获取登录图片
	wxData, _ := io.ReadAll(resp.Body)

	// 直接返回微信服务器的图片数据
	c.Data(
		http.StatusOK,
		"image/jpg",
		wxData,
	)
}

func (w *WechatHandler) ScanStatus(c *gin.Context) {
	// 创建请求 URL 和参数
	params := url.Values{}
	params.Set("action", "ask")
	params.Set("token", "")
	params.Set("lang", "zh_CN")
	params.Set("f", "json")
	params.Set("ajax", "1")

	// 创建请求
	req, err := http.NewRequest(
		http.MethodGet,
		"https://mp.weixin.qq.com/cgi-bin/scanloginqrcode?"+params.Encode(),
		nil,
	)
	if err != nil {
		slog.Error("create request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:    http.StatusInternalServerError,
			Message: "创建请求失败",
			Data:    nil,
		})
		return
	}

	// 设置 Cookie
	for _, cookie := range c.Request.Cookies() {
		req.AddCookie(&http.Cookie{Name: cookie.Name, Value: cookie.Value})
	}
	req.Header.Set("Referer", constants.WechatReferer)
	req.Header.Set("Origin", constants.WechatOrigin)
	req.Header.Set("User-Agent", constants.DefaultUserAgent)

	// 发送请求
	resp, err := defaultHTTPClient.Do(req)
	if err != nil {
		slog.Error("scan_status upstream request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusBadGateway, model.Response{
			Code:      http.StatusBadGateway,
			Message:   "上游服务不可用",
			ErrorCode: constants.BizErrUpstream,
			Data:      nil,
		})
		return
	}
	defer func(Body io.ReadCloser) {
		if err := Body.Close(); err != nil {
			slog.Debug("close body error", slog.String("op", "scan_status"), slog.String("error", err.Error()))
		}
	}(resp.Body)

	// 获取返回数据
	wxData, _ := io.ReadAll(resp.Body)
	wxDataStr := string(wxData)
	var wxDataJson map[string]interface{}
	err = json.Unmarshal([]byte(wxDataStr), &wxDataJson)
	if err != nil {
		slog.Warn("scan_status json unmarshal failed", slog.String("error", err.Error()))
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:      http.StatusInternalServerError,
			Message:   "JSON解析失败",
			ErrorCode: constants.BizErrJSONDecode,
			Data:      nil,
		})
		return
	}

	// 返回结果
	response.Success(c, wxDataJson)
}

func (w *WechatHandler) Login(c *gin.Context) {
	data := url.Values{
		"userlang":         {"zh_CN"},
		"redirect_url":     {""},
		"cookie_forbidden": {"0"},
		"cookie_cleaned":   {"0"},
		"plugin_used":      {"0"},
		"login_type":       {"3"},
		"token":            {""},
		"lang":             {"zh_CN"},
		"f":                {"json"},
		"ajax":             {"1"},
	}

	// 请求微信
	req, err := http.NewRequest(
		http.MethodPost,
		"https://mp.weixin.qq.com/cgi-bin/bizlogin?action=login",
		bytes.NewBufferString(data.Encode()),
	)
	if err != nil {
		slog.Error("create login request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:    http.StatusInternalServerError,
			Message: "创建登录请求失败",
			Data:    nil,
		})
		return
	}
	for _, cookie := range c.Request.Cookies() {
		req.AddCookie(&http.Cookie{Name: cookie.Name, Value: cookie.Value})
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Referer", constants.WechatReferer)
	req.Header.Set("Origin", constants.WechatOrigin)
	req.Header.Set("User-Agent", constants.DefaultUserAgent)
	resp, err := defaultHTTPClient.Do(req)
	if err != nil {
		slog.Error("login upstream request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusBadGateway, model.Response{
			Code:      http.StatusBadGateway,
			Message:   "上游服务不可用",
			ErrorCode: constants.BizErrUpstream,
			Data:      nil,
		})
		return
	}
	defer func(Body io.ReadCloser) {
		if err := Body.Close(); err != nil {
			slog.Debug("close body error", slog.String("op", "login"), slog.String("error", err.Error()))
		}
	}(resp.Body)

	// 将微信返回的 Set-Cookie 设置到返回结果中
	for _, sc := range resp.Header.Values("Set-Cookie") {
		c.Writer.Header().Add("Set-Cookie", sc)
	}
	// 微信返回内容
	wxData, _ := io.ReadAll(resp.Body)
	wxDataStr := string(wxData)

	// 提取 token
	var token string
	tokenPattern := `"redirect_url":"[^"]*[?&]token=([^"&]+)`
	tokenRegex := regexp.MustCompile(tokenPattern)
	matches := tokenRegex.FindStringSubmatch(wxDataStr)
	if len(matches) > 1 {
		token = matches[1]
	}

	// 解析 Cookie
	parsedCookies := ParseCookies(resp.Header.Values("Set-Cookie"))
	// 获取用户名和头像
	nickname, avatar := GetUserInfo(parsedCookies, token)
	if nickname == "" {
		// 仅记录最小必要信息，避免泄露敏感内容
		slog.Warn("login failed: empty nickname",
			slog.String("token_masked", maskToken(token)),
			slog.Int("cookie_count", len(parsedCookies)),
		)

		c.JSON(http.StatusBadRequest, model.Response{
			Code:      http.StatusBadRequest,
			Message:   "请选择公众号登录",
			ErrorCode: constants.BizErrEmptyNickname,
			Data:      nil,
		})
		return
	}

	// 搜索登录的公众号，获取 fakeid
	biz := SearchBiz("0", "5", nickname, token, parsedCookies)
	var fakeid string
	var bizJson map[string]interface{}
	err = json.Unmarshal([]byte(biz), &bizJson)
	if err != nil {
		slog.Warn("login SearchBiz json unmarshal failed",
			slog.String("error", err.Error()),
			slog.Int("resp_len", len(biz)),
		)
		c.JSON(http.StatusBadRequest, model.Response{
			Code:      http.StatusBadRequest,
			Message:   "登录失败，请稍后重试",
			ErrorCode: constants.BizErrJSONDecode,
			Data:      nil,
		})
		return
	}
	if list, ok := bizJson["list"].([]interface{}); ok && len(list) > 0 {
		for _, item := range list {
			if user, ok := item.(map[string]interface{}); ok {
				if user["nickname"].(string) == nickname {
					fakeid = user["fakeid"].(string)
					avatar = user["round_head_img"].(string)
				}
			}
		}
	}

	slaveUserCookie, exists := parsedCookies["slave_user"]
	if !exists {
		// 打印 cookie 名称列表，避免暴露值
		names := make([]string, 0, len(parsedCookies))
		for k := range parsedCookies {
			names = append(names, k)
		}
		slog.Warn("login failed: slave_user cookie missing", slog.Any("cookie_names", names))
		c.JSON(http.StatusBadRequest, model.Response{
			Code:      http.StatusBadRequest,
			Message:   "登录失败，请稍后重试",
			ErrorCode: constants.BizErrMissingCookie,
			Data:      nil,
		})
		return
	}

	// 创建用户
	user := map[string]interface{}{
		"id":          slaveUserCookie.Value,
		"fakeid":      fakeid,
		"nickname":    nickname,
		"avatar":      avatar,
		"token":       token,
		"login_time":  time.Now().Unix(),
		"expire_time": parseCookieTimestamp(slaveUserCookie.Expires),
	}

	for _, sc := range resp.Header.Values("Set-Cookie") {
		c.Writer.Header().Add("Set-Cookie", sc)
	}

	response.Success(c, user)
}

func parseCookieTimestamp(expiresStr string) int64 {
	// 标准化时间字符串
	timezones := map[string]string{
		"Gmt": "GMT",
		"Pst": "PST",
		"Est": "EST",
		"Cst": "CST",
		"Mst": "MST",
	}
	expiresStr = cases.Title(language.English).String(strings.ToLower(expiresStr))
	for oldStr, newStr := range timezones {
		expiresStr = strings.Replace(expiresStr, oldStr, newStr, 1)
	}

	formats := []string{
		time.RFC1123,                     // "Mon, 02 Jan 2006 15:04:05 MST"
		"Mon, 02-Jan-2006 15:04:05 MST",  // "Sat, 06-Sep-2025 18:44:57 GMT"
		"Mon, 02-Jan-06 15:04:05 MST",    // "Sat, 06-Sep-25 18:44:57 GMT"
		"Monday, 02-Jan-06 15:04:05 MST", // "Saturday, 06-Sep-25 18:44:57 GMT"
	}

	for _, layout := range formats {
		if t, err := time.Parse(layout, expiresStr); err == nil {
			return t.Unix()
		}
	}

	_ = fmt.Errorf("无法解析时间: %s", expiresStr)
	return 0
}

func (w *WechatHandler) SearchArticle(c *gin.Context) {
	fakeid := c.Query("fakeid")
	keyword := c.Query("keyword")
	token := c.Query("token")
	begin := c.DefaultQuery("begin", "0")
	count := c.DefaultQuery("count", "5")
	isSearching := keyword != ""

	// 创建请求 URL 和参数
	params := url.Values{}
	if isSearching {
		params.Set("sub", "search")
		params.Set("search_field", "7")
	} else {
		params.Set("sub", "list")
		params.Set("search_field", "null")
	}
	params.Set("begin", begin)
	params.Set("count", count)
	params.Set("query", keyword)
	params.Set("fakeid", fakeid)
	params.Set("type", "101_1")
	params.Set("free_publish_type", "1")
	params.Set("sub_action", "list_ex")
	params.Set("token", token)
	params.Set("lang", "zh_CN")
	params.Set("f", "json")
	params.Set("ajax", "1")

	// 创建请求
	req, err := http.NewRequest(
		http.MethodGet,
		"https://mp.weixin.qq.com/cgi-bin/appmsgpublish?"+params.Encode(),
		nil,
	)
	if err != nil {
		slog.Error("create search_article request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:    http.StatusInternalServerError,
			Message: "创建请求失败",
			Data:    nil,
		})
		return
	}

	// 设置 Cookie
	for _, cookie := range c.Request.Cookies() {
		req.AddCookie(&http.Cookie{Name: cookie.Name, Value: cookie.Value})
	}
	req.Header.Set("Referer", constants.WechatReferer)
	req.Header.Set("Origin", constants.WechatOrigin)
	req.Header.Set("User-Agent", constants.DefaultUserAgent)

	// 发送请求
	resp, err := defaultHTTPClient.Do(req)
	if err != nil {
		slog.Error("search_article upstream request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusBadGateway, model.Response{
			Code:      http.StatusBadGateway,
			Message:   "上游服务不可用",
			ErrorCode: constants.BizErrUpstream,
			Data:      nil,
		})
		return
	}
	defer func(Body io.ReadCloser) {
		if err := Body.Close(); err != nil {
			slog.Debug("close body error", slog.String("op", "search_article"), slog.String("error", err.Error()))
		}
	}(resp.Body)

	// 获取返回数据
	wxData, _ := io.ReadAll(resp.Body)
	wxDataStr := string(wxData)

	// 直接解析为 JSON 对象
	var wxDataJson map[string]interface{}
	if err := json.Unmarshal([]byte(wxDataStr), &wxDataJson); err != nil {
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:      http.StatusInternalServerError,
			Message:   "JSON解析失败",
			ErrorCode: constants.BizErrJSONDecode,
			Data:      nil,
		})
		return
	}

	// 处理 publish_page 字段，将其中的 publish_info 字符串转换为 JSON 对象
	if publishPageStr, ok := wxDataJson["publish_page"].(string); ok {
		var publishPageJson map[string]interface{}
		if err := json.Unmarshal([]byte(publishPageStr), &publishPageJson); err == nil {
			// 处理 publish_list 中的每个 publish_info
			if publishList, ok := publishPageJson["publish_list"].([]interface{}); ok {
				for _, item := range publishList {
					if itemMap, ok := item.(map[string]interface{}); ok {
						if publishInfoStr, ok := itemMap["publish_info"].(string); ok {
							var publishInfoJson map[string]interface{}
							if err := json.Unmarshal([]byte(publishInfoStr), &publishInfoJson); err == nil {
								itemMap["publish_info"] = publishInfoJson
							}
						}
					}
				}
			}
			wxDataJson["publish_page"] = publishPageJson
		}
	}

	// 返回处理后的JSON数据
	response.Success(c, wxDataJson)
}

func (w *WechatHandler) SearchAccount(c *gin.Context) {
	cookies := map[string]CookieItem{}
	for _, cookie := range c.Request.Cookies() {
		cookies[cookie.Name] = CookieItem{
			Name:  cookie.Name,
			Value: cookie.Value,
		}
	}

	// 搜索公众号/服务号
	wxDataStr := SearchBiz(c.Query("begin"), c.Query("count"), c.Query("keyword"), c.Query("token"), cookies)

	var wxDataJson map[string]interface{}
	err := json.Unmarshal([]byte(wxDataStr), &wxDataJson)
	if err != nil {
		slog.Warn("search_account json unmarshal failed", slog.String("error", err.Error()))
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:      http.StatusInternalServerError,
			Message:   "JSON解析失败",
			ErrorCode: constants.BizErrJSONDecode,
			Data:      nil,
		})
		return
	}

	response.Success(c, wxDataJson)
}

func (w *WechatHandler) DownloadArticle(c *gin.Context) {
	targetUrl := c.Query("url")

	// SSRF 防护：限制域名与协议
	if strings.TrimSpace(targetUrl) == "" {
		response.ErrorWithCode(c, errcode.ParamRequired, "url参数不能为空")
		return
	}

	u, err := url.Parse(targetUrl)
	if err != nil || (u.Scheme != "http" && u.Scheme != "https") || u.Hostname() == "" {
		response.URLError(c, "URL格式无效")
		return
	}

	// 允许下载的域名
	allowed := constants.WechatAllowedHosts
	if !isHostAllowed(u.Hostname(), allowed) {
		response.ErrorWithCode(c, errcode.URLNotAllowed, "不被允许的目标域名")
		return
	}

	// 自定义客户端：限制重定向到白名单之外
	client := &http.Client{
		Timeout: constants.DownloadTimeout * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !isHostAllowed(req.URL.Hostname(), allowed) {
				return fmt.Errorf("redirect to disallowed host: %s", req.URL.Hostname())
			}
			if len(via) >= constants.MaxRedirects {
				return fmt.Errorf("too many redirects")
			}
			return nil
		},
	}

	// 创建请求
	req, err := http.NewRequest(http.MethodGet, targetUrl, nil)
	if err != nil {
		slog.Error("create download_article request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:    http.StatusInternalServerError,
			Message: "创建请求失败",
			Data:    nil,
		})
		return
	}

	// 设置 Header（模拟浏览器，提升成功率）
	req.Header.Set("Accept",
		"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Referer", constants.WechatReferer)
	req.Header.Set("Sec-Ch-Ua", `"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"Windows"`)
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("User-Agent", constants.DefaultUserAgent)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		slog.Error("download_article upstream request failed", slog.String("error", err.Error()))
		c.JSON(http.StatusBadGateway, model.Response{
			Code:      http.StatusBadGateway,
			Message:   "上游服务不可用",
			ErrorCode: constants.BizErrUpstream,
			Data:      nil,
		})
		return
	}
	defer func(Body io.ReadCloser) {
		if err := Body.Close(); err != nil {
			slog.Debug("close body error", slog.String("op", "download_article"), slog.String("error", err.Error()))
		}
	}(resp.Body)

	// 获取返回数据
	// 限制最大响应体，防止内存滥用
	limited := io.LimitReader(resp.Body, constants.MaxResponseSize)
	wxData, _ := io.ReadAll(limited)
	wxDataStr := string(wxData)

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(wxDataStr))
	if err != nil {
		slog.Error("解析HTML失败", slog.String("error", err.Error()))
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:      http.StatusInternalServerError,
			Message:   "解析HTML失败",
			ErrorCode: constants.BizErrHTMLParse,
			Data:      nil,
		})
		return
	}

	// 提取正文
	content := doc.Find("div.rich_media_content")
	if content.Length() == 0 {
		slog.Warn("未找到正文容器")
		c.JSON(http.StatusInternalServerError, model.Response{
			Code:      http.StatusInternalServerError,
			Message:   "未找到正文容器",
			ErrorCode: constants.BizErrHTMLContentMissing,
			Data:      nil,
		})
		return
	}

	var parsedContent string
	// 提取所有文本段落
	content.Find("p").Each(func(i int, p *goquery.Selection) {
		text := p.Text()
		if strings.TrimSpace(text) != "" {
			parsedContent += text
		}
	})

	response.Success(c, parsedContent)
}

func SearchBiz(begin string, count string, keyword string, token string, cookies map[string]CookieItem) string {
	params := url.Values{
		"action": {"search_biz"},
		"begin":  {begin},
		"count":  {count},
		"query":  {keyword},
		"token":  {token},
		"lang":   {"zh_CN"},
		"f":      {"json"},
		"ajax":   {"1"},
	}

	// 请求微信
	req, err := http.NewRequest(
		http.MethodGet,
		"https://mp.weixin.qq.com/cgi-bin/searchbiz?"+params.Encode(),
		nil,
	)
	if err != nil {
		slog.Error("create search_biz request failed", slog.String("error", err.Error()))
		return ""
	}
	for _, value := range cookies {
		req.AddCookie(&http.Cookie{Name: value.Name, Value: value.Value})
	}
	req.Header.Set("Referer", constants.WechatReferer)
	req.Header.Set("Origin", constants.WechatOrigin)
	req.Header.Set("User-Agent", constants.DefaultUserAgent)
	resp, err := defaultHTTPClient.Do(req)
	if err != nil {
		slog.Error("search_biz upstream request failed", slog.String("error", err.Error()))
		return ""
	}
	defer func(Body io.ReadCloser) {
		if err := Body.Close(); err != nil {
			slog.Debug("close body error", slog.String("op", "search_biz"), slog.String("error", err.Error()))
		}
	}(resp.Body)

	// 透传微信结果
	wxData, _ := io.ReadAll(resp.Body)
	wxDataStr := string(wxData)
	return wxDataStr
}

func GetUserInfo(cookies map[string]CookieItem, token string) (nickname, avatar string) {
	// 创建请求 URL 和参数
	params := url.Values{}
	params.Set("t", "home/index")
	params.Set("token", token)
	params.Set("lang", "zh_CN")

	// 创建请求
	req, err := http.NewRequest(
		http.MethodGet,
		"https://mp.weixin.qq.com/cgi-bin/home?"+params.Encode(),
		nil,
	)
	if err != nil {
		slog.Error("create get_user_info request failed", slog.String("error", err.Error()))
		return "", ""
	}

	// 设置 Cookie
	for key, value := range cookies {
		req.AddCookie(&http.Cookie{Name: key, Value: value.Value})
	}
	req.Header.Set("Referer", constants.WechatReferer)
	req.Header.Set("Origin", constants.WechatOrigin)
	req.Header.Set("User-Agent", constants.DefaultUserAgent)

	// 发送请求
	resp, err := defaultHTTPClient.Do(req)
	if err != nil {
		slog.Error("get_user_info upstream request failed", slog.String("error", err.Error()))
		return "", ""
	}
	defer func(Body io.ReadCloser) {
		if err := Body.Close(); err != nil {
			slog.Debug("close body error", slog.String("op", "get_user_info"), slog.String("error", err.Error()))
		}
	}(resp.Body)

	// 获取返回数据
	wxData, _ := io.ReadAll(resp.Body)
	html := string(wxData)

	// 提取昵称
	var nickName string
	nicknamePattern := `wx\.cgiData\.nick_name\s*=\s*"([^"]+)"`
	nicknameRegex := regexp.MustCompile(nicknamePattern)
	nicknameMatches := nicknameRegex.FindStringSubmatch(html)
	if len(nicknameMatches) > 1 {
		nickName = nicknameMatches[1]
	}

	// 提取头像
	var headImg string
	headImgPattern := `wx\.cgiData\.head_img\s*=\s*"([^"]+)"`
	headImgRegex := regexp.MustCompile(headImgPattern)
	headImgMatches := headImgRegex.FindStringSubmatch(html)
	if len(headImgMatches) > 1 {
		headImg = headImgMatches[1]
	}

	return nickName, headImg
}

// 校验主机名是否在白名单中
func isHostAllowed(host string, allowed []string) bool {
	if host == "" {
		return false
	}
	h := strings.ToLower(host)
	for _, a := range allowed {
		if strings.ToLower(strings.TrimSpace(a)) == h {
			return true
		}
	}
	return false
}

// maskToken 对令牌进行部分脱敏
func maskToken(t string) string {
	if len(t) <= 6 {
		return "***"
	}
	return t[:3] + "***" + t[len(t)-3:]
}

func ParseCookies(cookies []string) map[string]CookieItem {
	result := make(map[string]CookieItem)

	for _, cookie := range cookies {
		// 按分号分割并去除空白字符
		parts := strings.Split(cookie, ";")
		for i := range parts {
			parts[i] = strings.TrimSpace(parts[i])
		}

		if len(parts) == 0 {
			continue
		}

		// 解析名称和值
		nameValue := strings.SplitN(parts[0], "=", 2)
		if len(nameValue) != 2 {
			continue
		}

		name := nameValue[0]
		value := nameValue[1]

		// 处理其他属性
		other := make([]string, len(parts)-1)
		for i, part := range parts[1:] {
			other[i] = strings.ToLower(part)
		}

		// 查找path属性
		path := "/"
		for _, part := range other {
			if strings.HasPrefix(part, "path=") {
				pathParts := strings.SplitN(part, "=", 2)
				if len(pathParts) == 2 {
					path = pathParts[1]
				}
				break
			}
		}

		// 查找expires属性
		expires := ""
		for _, part := range other {
			if strings.HasPrefix(part, "expires=") {
				expiresParts := strings.SplitN(part, "=", 2)
				if len(expiresParts) == 2 {
					expires = expiresParts[1]
				}
				break
			}
		}

		// 检查secure标志
		secure := false
		for _, part := range other {
			if part == "secure" {
				secure = true
				break
			}
		}

		// 检查httponly标志
		httpOnly := false
		for _, part := range other {
			if part == "httponly" {
				httpOnly = true
				break
			}
		}

		result[name] = CookieItem{
			Name:     name,
			Value:    value,
			Path:     path,
			Expires:  expires,
			Secure:   secure,
			HttpOnly: httpOnly,
		}
	}

	return result
}
