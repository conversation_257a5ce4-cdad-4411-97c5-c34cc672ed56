package constants

// Database connection pool settings
const (
	MaxOpenConns = 25
	MaxIdleConns = 25
)

// Application info
const (
	AppVersion = "1.0.0"
)

// WeChat API configuration
const (
	DefaultUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
	WechatReferer    = "https://mp.weixin.qq.com/"
	WechatOrigin     = "https://mp.weixin.qq.com"
)

// WeChat allowed hosts
var WechatAllowedHosts = []string{
	"mp.weixin.qq.com",
}

// HTTP client settings
const (
	DefaultTimeout  = 30 // seconds
	DownloadTimeout = 15 // seconds
	MaxRedirects    = 5
	MaxResponseSize = 5 << 20 // 5MB
)

// WeChat login status codes
const (
	LoginStatusWaiting   = 0  // 等待扫码
	LoginStatusScanned   = 1  // 已扫码，等待确认
	LoginStatusConfirmed = 2  // 已确认，登录成功
	LoginStatusExpired   = 3  // 二维码已过期
	LoginStatusCancelled = 4  // 用户取消登录
	LoginStatusError     = -1 // 检查出错
)

// Business error codes（稳定、可供前端与监控使用）
const (
	BizErrUpstream            = "UPSTREAM_ERROR"
	BizErrJSONDecode          = "JSON_DECODE_ERROR"
	BizErrHTMLParse           = "HTML_PARSE_ERROR"
	BizErrHTMLContentMissing  = "HTML_CONTENT_MISSING"
	BizErrMissingCookie       = "MISSING_COOKIE"
	BizErrEmptyNickname       = "EMPTY_NICKNAME"
	BizErrInvalidParams       = "INVALID_PARAMS"
	BizErrDatabaseUnreachable = "DATABASE_UNREACHABLE"
)
