package util

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
)

type Method string

const (
	GET    Method = "GET"
	POST   Method = "POST"
	PUT    Method = "PUT"
	PATCH  Method = "PATCH"
	DELETE Method = "DELETE"
)

type RequestOptions struct {
	Endpoint        string                 `json:"endpoint"`        // 请求端点
	Method          Method                 `json:"method"`          // 请求方法
	Query           map[string]interface{} `json:"query,omitempty"` // 查询参数
	Body            map[string]interface{} `json:"body,omitempty"`  // 请求体
	ParseJSON       bool                   `json:"parseJson"`       // 是否解析JSON响应
	WithCredentials bool                   `json:"withCredentials"` // 是否携带认证信息
	Cookies         map[string]string      `json:"cookies"`         // Cookie信息
}

type Client struct {
	httpClient *http.Client
	request    *http.Request
}

// NewClient 创建新的客户端实例
func NewClient() *Client {
	return &Client{
		httpClient: &http.Client{},
		request:    &http.Request{},
	}
}

// ProxyMPRequest 执行微信公众号API请求
func (c *Client) ProxyMPRequest(options RequestOptions) (*http.Response, error) {
	// 如果没有显式设置WithCredentials，默认为true
	//if options.WithCredentials {
	//	options.Cookies = c.cookies
	//}

	// 构建完整的URL
	fullURL, err := c.buildURL(options.Endpoint, options.Query)
	if err != nil {
		return nil, fmt.Errorf("构建URL失败: %w", err)
	}

	// 创建请求
	req, err := c.createRequest(string(options.Method), fullURL, options)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 执行请求
	return c.httpClient.Do(req)
}

// ProxyMPRequestJSON 执行请求并解析JSON响应
func (c *Client) ProxyMPRequestJSON(options RequestOptions, result interface{}) error {
	options.ParseJSON = true
	resp, err := c.ProxyMPRequest(options)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应体失败: %w", err)
	}

	return json.Unmarshal(body, result)
}

// buildURL 构建完整的URL（包含查询参数）
func (c *Client) buildURL(endpoint string, query map[string]interface{}) (string, error) {
	u, err := url.Parse(endpoint)
	if err != nil {
		return "", err
	}

	if query != nil && len(query) > 0 {
		values := url.Values{}
		for key, value := range query {
			if value != nil {
				values.Add(key, c.interfaceToString(value))
			}
		}
		u.RawQuery = values.Encode()
	}

	return u.String(), nil
}

// createRequest 创建HTTP请求
func (c *Client) createRequest(method, requestURL string, options RequestOptions) (*http.Request, error) {
	var body io.Reader

	// 处理POST请求体
	if method == "POST" && options.Body != nil {
		formData := url.Values{}
		for key, value := range options.Body {
			if value != nil {
				formData.Set(key, c.interfaceToString(value))
			}
		}
		body = strings.NewReader(formData.Encode())
	}

	req, err := http.NewRequest(method, requestURL, body)
	if err != nil {
		return nil, err
	}

	// 设置默认请求头
	c.setDefaultHeaders(req)

	// 设置Cookie
	if options.WithCredentials && options.Cookies != nil {
		c.setCookies(req, options.Cookies)
	}

	// 如果是POST请求，设置Content-Type
	if method == "POST" && options.Body != nil {
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	}

	return req, nil
}

func (c *Client) setDefaultHeaders(req *http.Request) {
	req.Header.Set("Referer", "https://mp.weixin.qq.com/")
	req.Header.Set("Origin", "https://mp.weixin.qq.com")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36")
}

func (c *Client) setCookies(req *http.Request, cookies map[string]string) {
	var cookieParts []string
	for key, value := range cookies {
		cookieParts = append(cookieParts, fmt.Sprintf("%s=%s", key, value))
	}
	if len(cookieParts) > 0 {
		req.Header.Set("Cookie", strings.Join(cookieParts, "; "))
	}
}

// interfaceToString 将interface{}转换为字符串
func (c *Client) interfaceToString(value interface{}) string {
	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// Get 执行GET请求
func (c *Client) Get(endpoint string, query map[string]interface{}) (*http.Response, error) {
	return c.ProxyMPRequest(RequestOptions{
		Endpoint:        endpoint,
		Method:          GET,
		Query:           query,
		WithCredentials: true,
	})
}

// Post 执行POST请求
func (c *Client) Post(endpoint string, body map[string]interface{}) (*http.Response, error) {
	return c.ProxyMPRequest(RequestOptions{
		Endpoint:        endpoint,
		Method:          POST,
		Body:            body,
		WithCredentials: true,
	})
}

// GetJSON 执行GET请求并解析JSON
func (c *Client) GetJSON(endpoint string, query map[string]interface{}, result interface{}) error {
	return c.ProxyMPRequestJSON(RequestOptions{
		Endpoint:        endpoint,
		Method:          GET,
		Query:           query,
		WithCredentials: true,
	}, result)
}

// PostJSON 执行POST请求并解析JSON
func (c *Client) PostJSON(endpoint string, body map[string]interface{}, result interface{}) error {
	return c.ProxyMPRequestJSON(RequestOptions{
		Endpoint:        endpoint,
		Method:          POST,
		Body:            body,
		WithCredentials: true,
	}, result)
}
