package middleware

import (
	"log/slog"
	"time"

	"github.com/gin-gonic/gin"
)

// <PERSON><PERSON> creates a structured logging middleware with request tracking
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// Extract request ID from context
		requestID := getRequestIDFromKeys(param.Keys)
		if requestID == "" {
			requestID = "unknown"
		}

		// Create structured log entry
		slog.Info("HTTP request",
			slog.String("request_id", requestID),
			slog.String("method", param.Method),
			slog.String("path", param.Path),
			slog.Int("status", param.StatusCode),
			slog.String("ip", param.ClientIP),
			slog.String("user_agent", param.Request.UserAgent()),
			slog.Duration("latency", param.Latency),
			slog.String("time", param.TimeStamp.Format(time.RFC3339)),
		)

		// Return empty string since we're using structured logging
		return ""
	})
}

// getRequestIDFromKeys retrieves request ID from gin keys (used in logging)
func getRequestIDFromKeys(keys map[string]interface{}) string {
	if keys == nil {
		return ""
	}

	if requestID, exists := keys[RequestIDKey]; exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}
