package middleware

import (
	"github.com/gin-gonic/gin"
)

// SecurityHeaders adds basic security headers to HTTP responses
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Prevent MIME type sniffing
		c.<PERSON><PERSON>("X-Content-Type-Options", "nosniff")

		// Enable XSS protection
		c.<PERSON><PERSON>("X-XSS-Protection", "1; mode=block")

		// Prevent page from being displayed in frame/iframe
		c.<PERSON><PERSON>("X-Frame-Options", "DENY")

		// Enforce HTTPS in production (modify based on environment)
		// c.<PERSON><PERSON>("Strict-Transport-Security", "max-age=31536000; includeSubDomains")

		// Referrer policy
		c.<PERSON>er("Referrer-Policy", "strict-origin-when-cross-origin")

		// Content Security Policy (basic)
		c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data:")

		// Permissions Policy (formerly Feature Policy)
		c.<PERSON><PERSON>("Permissions-Policy", "camera=(), microphone=(), geolocation=()")

		c.Next()
	}
}
