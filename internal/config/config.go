package config

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/viper"
)

type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Log      LogConfig      `yaml:"log"`
	Database DatabaseConfig `yaml:"database"`
	CORS     CORSConfig     `yaml:"cors"`
}

type ServerConfig struct {
	Port string `yaml:"port"`
	Mode string `yaml:"mode"`
}

type LogConfig struct {
	Level      string `yaml:"level"`
	File       string `yaml:"file"`
	MaxSize    int    `yaml:"max_size"`
	MaxAge     int    `yaml:"max_age"`
	MaxBackups int    `yaml:"max_backups"`
}

type DatabaseConfig struct {
	Host            string        `yaml:"host"`
	Port            int           `yaml:"port"`
	User            string        `yaml:"user"`
	Password        string        `yaml:"password"`
	Name            string        `yaml:"name"`
	SSLMode         string        `yaml:"sslmode"`
	DSN             string        `yaml:"dsn"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time"`
}

type CORSConfig struct {
	AllowedOrigins   []string `yaml:"allowed_origins"`
	AllowedMethods   []string `yaml:"allowed_methods"`
	AllowedHeaders   []string `yaml:"allowed_headers"`
	AllowCredentials bool     `yaml:"allow_credentials"`
}

func Load() (*Config, error) {
	// 获取环境变量，默认为 dev（本地开发）
	env := getEnv("APP_ENV", "dev")

	// 根据环境设置配置文件名
	configName, err := getConfigName(env)
	if err != nil {
		return nil, fmt.Errorf("invalid environment configuration: %w", err)
	}

	viper.SetConfigName(configName)
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 设置环境变量前缀
	viper.SetEnvPrefix("WX_CRAWLER")
	// 允许使用下划线覆盖嵌套 key，如 WX_CRAWLER_DATABASE_HOST
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 设置默认值
	setDefaults()

	// 绑定常用环境变量（兼容无前缀的 DB_* / SERVER_PORT 等）
	bindEnvVars()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		// 如果配置文件不存在，使用默认值
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 配置校验（prod 环境约束更严格）
	if err := validateConfig(env, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

// setDefaults 设置默认值
func setDefaults() {
	// 服务器默认值
	viper.SetDefault("server.port", "8080")
	viper.SetDefault("server.mode", "debug")

	// 日志默认值（更接近生产的默认）
	viper.SetDefault("log.level", "debug")
	viper.SetDefault("log.file", "logs/app.log")
	viper.SetDefault("log.max_size", 50)
	viper.SetDefault("log.max_age", 30)
	viper.SetDefault("log.max_backups", 10)

	// 数据库默认值
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "wx_crawler")
	viper.SetDefault("database.password", "wx_crawler")
	viper.SetDefault("database.name", "wx_crawler")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.dsn", "")
	viper.SetDefault("database.max_open_conns", 10)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "5m")
	viper.SetDefault("database.conn_max_idle_time", "0s")

	// CORS默认值
	viper.SetDefault("cors.allowed_origins", []string{"*"})
	viper.SetDefault("cors.allowed_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"})
	viper.SetDefault("cors.allowed_headers", []string{"Content-Type", "Authorization"})
	viper.SetDefault("cors.allow_credentials", true)
}

// bindEnvVars 绑定环境变量覆盖（兼容常见变量名）
func bindEnvVars() {
	// 服务器
	_ = viper.BindEnv("server.port", "SERVER_PORT")

	// 日志
	_ = viper.BindEnv("log.level", "LOG_LEVEL")
	_ = viper.BindEnv("log.file", "LOG_FILE")
	_ = viper.BindEnv("log.max_size", "LOG_MAX_SIZE")
	_ = viper.BindEnv("log.max_age", "LOG_MAX_AGE")
	_ = viper.BindEnv("log.max_backups", "LOG_MAX_BACKUPS")

	// 数据库（兼容常见变量名）
	_ = viper.BindEnv("database.dsn", "DB_DSN")
	_ = viper.BindEnv("database.dsn", "SQL_DSN")
	_ = viper.BindEnv("database.host", "DB_HOST")
	_ = viper.BindEnv("database.port", "DB_PORT")
	_ = viper.BindEnv("database.user", "DB_USER")
	_ = viper.BindEnv("database.password", "DB_PASSWORD")
	_ = viper.BindEnv("database.name", "DB_NAME")
	_ = viper.BindEnv("database.sslmode", "DB_SSLMODE")
	_ = viper.BindEnv("database.max_open_conns", "SQL_MAX_OPEN_CONNS")
	_ = viper.BindEnv("database.max_idle_conns", "SQL_MAX_IDLE_CONNS")
	_ = viper.BindEnv("database.conn_max_lifetime", "SQL_CONN_MAX_LIFETIME")
}

// getEnv 获取环境变量，如果不存在返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getConfigName 根据环境返回配置文件名
func getConfigName(env string) (string, error) {
	switch strings.ToLower(env) {
	case "prod", "production":
		return "config.prod", nil
	case "dev", "development", "":
		return "config.dev", nil
	default:
		return "", fmt.Errorf("unsupported environment: %s (supported: dev, prod)", env)
	}
}

// validateConfig 根据环境校验配置正确性
func validateConfig(env string, cfg *Config) error {
    switch strings.ToLower(env) {
    case "prod", "production":
        // 当使用 DSN 时，不再强制校验单独字段，但禁止显式使用 sslmode=disable
        if strings.TrimSpace(cfg.Database.DSN) != "" {
            dsnLower := strings.ToLower(cfg.Database.DSN)
            if strings.Contains(dsnLower, "sslmode=disable") {
                return fmt.Errorf("database dsn must not set sslmode=disable in production")
            }
        } else {
            // 未使用 DSN：密码必填，且 sslmode 要求更安全
            if strings.TrimSpace(cfg.Database.Password) == "" {
                return fmt.Errorf("database password required in production (set DB_PASSWORD or WX_CRAWLER_DATABASE_PASSWORD)")
            }
            ssl := strings.ToLower(strings.TrimSpace(cfg.Database.SSLMode))
            if ssl == "" || ssl == "disable" {
                return fmt.Errorf("database sslmode should be 'require' (or stronger) in production")
            }
        }
        // CORS 不允许通配符
        for _, o := range cfg.CORS.AllowedOrigins {
            if o == "*" {
                return fmt.Errorf("cors.allowed_origins must not contain '*' in production")
            }
		}
	default:
		// dev 环境不做强校验
	}
	return nil
}
