package response

import (
	"wx-crawler/internal/errcode"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Detail  string      `json:"detail,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.<PERSON>(errcode.Success.GetHTTPStatus(), Response{
		Code:    int(errcode.Success),
		Message: errcode.GetMessage(errcode.Success),
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, err *errcode.Error) {
	c.JSON(err.Code.GetHTTPStatus(), Response{
		Code:    int(err.Code),
		Message: err.Message,
		Detail:  err.Detail,
	})
}

// ErrorWithCode 指定错误码的错误响应
func ErrorWithCode(c *gin.Context, code errcode.ErrorCode, detail string) {
	err := errcode.NewError(code, detail)
	Error(c, err)
}

// SystemError 系统错误响应
func SystemError(c *gin.Context, detail string) {
	ErrorWithCode(c, errcode.SystemError, detail)
}

// ParamError 参数错误响应
func ParamError(c *gin.Context, detail string) {
	ErrorWithCode(c, errcode.ParamInvalid, detail)
}

// DatabaseError 数据库错误响应
func DatabaseError(c *gin.Context, detail string) {
	ErrorWithCode(c, errcode.DatabaseError, detail)
}

// WechatError 微信API错误响应
func WechatError(c *gin.Context, detail string) {
	ErrorWithCode(c, errcode.WechatAPIError, detail)
}

// JSONError JSON解析错误响应
func JSONError(c *gin.Context, detail string) {
	ErrorWithCode(c, errcode.JSONFormatError, detail)
}

// URLError URL错误响应
func URLError(c *gin.Context, detail string) {
	ErrorWithCode(c, errcode.URLInvalid, detail)
}

// RequestError 请求创建失败响应
func RequestError(c *gin.Context, detail string) {
	ErrorWithCode(c, errcode.RequestCreateFailed, detail)
}