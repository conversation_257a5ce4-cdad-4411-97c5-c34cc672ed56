package errcode

import "net/http"

// ErrorCode 错误码类型
type ErrorCode int

// Error 错误信息结构
type Error struct {
	Code    ErrorCode `json:"code"`
	Message string    `json:"message"`
	Detail  string    `json:"detail,omitempty"`
}

func (e Error) Error() string {
	return e.Message
}

// NewError 创建新的错误
func NewError(code ErrorCode, detail string) *Error {
	return &Error{
		Code:    code,
		Message: GetMessage(code),
		Detail:  detail,
	}
}

// GetHTTPStatus 根据错误码获取HTTP状态码
func (e ErrorCode) GetHTTPStatus() int {
	switch {
	case e == Success:
		return http.StatusOK
	case e >= 20000 && e < 30000: // 参数验证错误
		return http.StatusBadRequest
	case e >= 30000 && e < 40000: // 数据库错误
		return http.StatusInternalServerError
	case e >= 40000 && e < 50000: // 微信API错误
		return http.StatusBadGateway
	case e >= 50000 && e < 60000: // 第三方服务错误
		return http.StatusBadGateway
	default:
		return http.StatusInternalServerError
	}
}

// 成功
const (
	Success ErrorCode = 10000 // 成功
)

// 系统级别错误 (1xxxx)
const (
	SystemError          ErrorCode = 12001 // 系统内部错误
	SystemTimeout        ErrorCode = 12002 // 系统超时
	SystemUnavailable    ErrorCode = 12003 // 系统不可用
	SystemConfigError    ErrorCode = 12004 // 系统配置错误
	SystemResourceLimit  ErrorCode = 12005 // 系统资源限制
)

// 参数验证错误 (2xxxx)
const (
	ParamRequired        ErrorCode = 21001 // 必需参数缺失
	ParamInvalid         ErrorCode = 21002 // 参数格式无效
	ParamOutOfRange      ErrorCode = 21003 // 参数超出范围
	ParamTooLong         ErrorCode = 21004 // 参数过长
	ParamTooShort        ErrorCode = 21005 // 参数过短
	ParamFormatError     ErrorCode = 21006 // 参数格式错误
	URLInvalid           ErrorCode = 21007 // URL格式无效
	URLNotAllowed        ErrorCode = 21008 // URL不被允许
	JSONFormatError      ErrorCode = 21009 // JSON格式错误
)

// 数据库错误 (3xxxx)
const (
	DatabaseError         ErrorCode = 32001 // 数据库错误
	DatabaseConnectFailed ErrorCode = 32002 // 数据库连接失败
	DatabaseTimeout       ErrorCode = 32003 // 数据库超时
	DatabaseRecordNotFound ErrorCode = 32004 // 记录不存在
	DatabaseDuplicateKey  ErrorCode = 32005 // 主键冲突
	DatabaseConstraint    ErrorCode = 32006 // 约束冲突
)

// 微信API错误 (4xxxx)
const (
	WechatAPIError       ErrorCode = 42001 // 微信API错误
	WechatTimeout        ErrorCode = 42002 // 微信API超时
	WechatUnauthorized   ErrorCode = 42003 // 微信认证失败
	WechatRateLimit      ErrorCode = 42004 // 微信API限流
	WechatInvalidToken   ErrorCode = 42005 // 微信Token无效
	WechatQRCodeExpired  ErrorCode = 42006 // 二维码已过期
	WechatLoginFailed    ErrorCode = 42007 // 微信登录失败
	WechatSessionExpired ErrorCode = 42008 // 微信会话过期
	WechatCookieMissing  ErrorCode = 42009 // 微信Cookie缺失
	WechatAccountEmpty   ErrorCode = 42010 // 微信账号信息为空
	WechatParseError     ErrorCode = 42011 // 微信响应解析错误
	WechatContentMissing ErrorCode = 42012 // 微信内容缺失
)

// 第三方服务错误 (5xxxx)
const (
	ExternalAPIError     ErrorCode = 52001 // 第三方API错误
	ExternalTimeout      ErrorCode = 52002 // 第三方服务超时
	ExternalUnavailable  ErrorCode = 52003 // 第三方服务不可用
	ExternalAuthFailed   ErrorCode = 52004 // 第三方认证失败
	ExternalRateLimit    ErrorCode = 52005 // 第三方服务限流
	HTMLParseError       ErrorCode = 52006 // HTML解析错误
	NetworkError         ErrorCode = 52007 // 网络错误
	RequestCreateFailed  ErrorCode = 52008 // 请求创建失败
)

// GetMessage 根据错误码获取错误消息
func GetMessage(code ErrorCode) string {
	messages := map[ErrorCode]string{
		// 成功
		Success: "成功",

		// 系统级别错误
		SystemError:          "系统内部错误",
		SystemTimeout:        "系统超时",
		SystemUnavailable:    "系统不可用",
		SystemConfigError:    "系统配置错误",
		SystemResourceLimit:  "系统资源限制",

		// 参数验证错误
		ParamRequired:    "必需参数缺失",
		ParamInvalid:     "参数格式无效",
		ParamOutOfRange:  "参数超出范围",
		ParamTooLong:     "参数过长",
		ParamTooShort:    "参数过短",
		ParamFormatError: "参数格式错误",
		URLInvalid:       "URL格式无效",
		URLNotAllowed:    "URL不被允许",
		JSONFormatError:  "JSON格式错误",

		// 数据库错误
		DatabaseError:          "数据库错误",
		DatabaseConnectFailed:  "数据库连接失败",
		DatabaseTimeout:        "数据库超时",
		DatabaseRecordNotFound: "记录不存在",
		DatabaseDuplicateKey:   "主键冲突",
		DatabaseConstraint:     "约束冲突",

		// 微信API错误
		WechatAPIError:       "微信API错误",
		WechatTimeout:        "微信API超时",
		WechatUnauthorized:   "微信认证失败",
		WechatRateLimit:      "微信API限流",
		WechatInvalidToken:   "微信Token无效",
		WechatQRCodeExpired:  "二维码已过期",
		WechatLoginFailed:    "微信登录失败",
		WechatSessionExpired: "微信会话过期",
		WechatCookieMissing:  "微信Cookie缺失",
		WechatAccountEmpty:   "微信账号信息为空",
		WechatParseError:     "微信响应解析错误",
		WechatContentMissing: "微信内容缺失",

		// 第三方服务错误
		ExternalAPIError:    "第三方API错误",
		ExternalTimeout:     "第三方服务超时",
		ExternalUnavailable: "第三方服务不可用",
		ExternalAuthFailed:  "第三方认证失败",
		ExternalRateLimit:   "第三方服务限流",
		HTMLParseError:      "HTML解析错误",
		NetworkError:        "网络错误",
		RequestCreateFailed: "请求创建失败",
	}

	if message, exists := messages[code]; exists {
		return message
	}
	return "未知错误"
}

// 常用错误创建函数
func NewSystemError(detail string) *Error {
	return NewError(SystemError, detail)
}

func NewParamError(detail string) *Error {
	return NewError(ParamInvalid, detail)
}

func NewDatabaseError(detail string) *Error {
	return NewError(DatabaseError, detail)
}

func NewWechatError(detail string) *Error {
	return NewError(WechatAPIError, detail)
}

func NewExternalError(detail string) *Error {
	return NewError(ExternalAPIError, detail)
}