package database

import (
	"database/sql"
	"fmt"
	"time"

	_ "github.com/lib/pq" // PostgreSQL driver

	"wx-crawler/internal/config"
)

// Connect 建立 PostgreSQL 连接，支持 DSN 与参数拼接，使用配置的连接池参数
func Connect(cfg config.DatabaseConfig) (*sql.DB, error) {
	// 优先使用 DSN；否则使用参数拼接
	dsn := cfg.DSN
	if dsn == "" {
		dsn = fmt.Sprintf("host=%s port=%d user=%s password=%s name=%s sslmode=%s",
			cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.Name, cfg.SSLMode)
	}

	// 打开连接
	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 配置连接池
	if cfg.MaxOpenConns > 0 {
		db.SetMaxOpenConns(cfg.MaxOpenConns)
	}
	if cfg.MaxIdleConns > 0 {
		db.SetMaxIdleConns(cfg.MaxIdleConns)
	}
	if cfg.ConnMaxLifetime > 0 {
		db.SetConnMaxLifetime(cfg.ConnMaxLifetime)
	}
	if cfg.ConnMaxIdleTime > 0 {
		// Go 1.20+ 可用；向后兼容不报错
		db.SetConnMaxIdleTime(cfg.ConnMaxIdleTime)
	}

	// 验证连接
	// 设置一个有限超时的 Ping 以避免无限等待
	pingDeadline := time.After(5 * time.Second)
	pingErrCh := make(chan error, 1)
	go func() { pingErrCh <- db.Ping() }()
	select {
	case err := <-pingErrCh:
		if err != nil {
			return nil, fmt.Errorf("failed to ping database: %w", err)
		}
	case <-pingDeadline:
		return nil, fmt.Errorf("failed to ping database: timeout")
	}

	return db, nil
}
